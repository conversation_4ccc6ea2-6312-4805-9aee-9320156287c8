export const customers = [
  {
    id: 1,
    name: "ČEZ Distribuce a.s.",
    email: "<EMAIL>",
    phone: "+420 211 046 111",
    address: "Teplická 874/8, 405 02 Děčín",
    status: "active",
    totalOrders: 24,
    totalValue: 2850000,
    industry: "Energetika",
    contractType: "Rámcová smlouva"
  },
  {
    id: 2,
    name: "Škoda Auto a.s.",
    email: "<EMAIL>",
    phone: "+420 326 811 111",
    address: "tř. Václava Klementa 869, 293 60 Mladá Boleslav",
    status: "active",
    totalOrders: 18,
    totalValue: 1920000,
    industry: "Automotive",
    contractType: "Dodavatelská smlouva"
  },
  {
    id: 3,
    name: "Siemens s.r.o.",
    email: "<EMAIL>",
    phone: "+420 233 033 111",
    address: "Siemensova 2717/4, 155 00 Praha 13",
    status: "active",
    totalOrders: 12,
    totalValue: 1450000,
    industry: "Pr<PERSON>my<PERSON><PERSON>",
    contractType: "<PERSON>je<PERSON><PERSON> smlouva"
  },
  {
    id: 4,
    name: "Tesco Stores ČR a.s.",
    email: "<EMAIL>",
    phone: "+420 296 411 111",
    address: "Vršovická 1527/68b, 101 00 Praha 10",
    status: "inactive",
    totalOrders: 6,
    totalValue: 380000,
    industry: "Retail",
    contractType: "Servisní smlouva"
  }
];

export const products = [
  {
    id: 1,
    name: "Silový kabel CYKY 3x2,5",
    sku: "CYKY-3x2.5-100",
    category: "Silové kabely",
    price: 45.50,
    priceUnit: "m",
    stock: 2500,
    stockUnit: "m",
    minStock: 500,
    description: "Měděný kabel pro pevné uložení, 450/750V",
    specification: "ČSN 34 7405-3",
    location: "Sklad A-1-15",
    supplier: "Nexans Czech Republic"
  },
  {
    id: 2,
    name: "Datový kabel UTP Cat6",
    sku: "UTP-CAT6-305",
    category: "Datové kabely",
    price: 12.80,
    priceUnit: "m",
    stock: 1850,
    stockUnit: "m",
    minStock: 300,
    description: "Nestíněný kroucený pár pro strukturovanou kabeláž",
    specification: "ISO/IEC 11801",
    location: "Sklad B-2-08",
    supplier: "Belden Electronics"
  },
  {
    id: 3,
    name: "Koaxiální kabel RG6",
    sku: "RG6-75OHM-500",
    category: "Koaxiální kabely",
    price: 8.90,
    priceUnit: "m",
    stock: 3200,
    stockUnit: "m",
    minStock: 800,
    description: "75Ω koaxiální kabel pro satelitní a kabelové systémy",
    specification: "IEC 60096-1",
    location: "Sklad A-3-22",
    supplier: "CommScope Technologies"
  },
  {
    id: 4,
    name: "Optický kabel 24vl SM",
    sku: "OPT-24SM-ADSS",
    category: "Optické kabely",
    price: 125.00,
    priceUnit: "m",
    stock: 850,
    stockUnit: "m",
    minStock: 200,
    description: "24-vláknový singlemode kabel pro venkovní použití",
    specification: "ITU-T G.652.D",
    location: "Sklad C-1-05",
    supplier: "Prysmian Group"
  },
  {
    id: 5,
    name: "Řídicí kabel LIYCY 12x0,75",
    sku: "LIYCY-12x0.75-100",
    category: "Řídicí kabely",
    price: 28.40,
    priceUnit: "m",
    stock: 1200,
    stockUnit: "m",
    minStock: 250,
    description: "Stíněný řídicí kabel pro automatizaci",
    specification: "VDE 0812",
    location: "Sklad B-1-12",
    supplier: "Lapp Kabel"
  }
];

export const orders = [
  {
    id: 2024001,
    customerId: 1,
    customerName: "ČEZ Distribuce a.s.",
    date: "2024-01-15",
    status: "completed",
    priority: "high",
    total: 125000,
    deliveryDate: "2024-02-15",
    items: [
      { productId: 1, productName: "Silový kabel CYKY 3x2,5", quantity: 2000, unit: "m", price: 45.50 },
      { productId: 3, productName: "Koaxiální kabel RG6", quantity: 500, unit: "m", price: 8.90 }
    ]
  },
  {
    id: 2024002,
    customerId: 2,
    customerName: "Škoda Auto a.s.",
    date: "2024-01-18",
    status: "production",
    priority: "medium",
    total: 89600,
    deliveryDate: "2024-02-20",
    items: [
      { productId: 2, productName: "Datový kabel UTP Cat6", quantity: 1500, unit: "m", price: 12.80 },
      { productId: 5, productName: "Řídicí kabel LIYCY 12x0,75", quantity: 800, unit: "m", price: 28.40 }
    ]
  },
  {
    id: 2024003,
    customerId: 3,
    customerName: "Siemens s.r.o.",
    date: "2024-01-20",
    status: "pending",
    priority: "high",
    total: 156250,
    deliveryDate: "2024-03-01",
    items: [
      { productId: 4, productName: "Optický kabel 24vl SM", quantity: 1250, unit: "m", price: 125.00 }
    ]
  },
  {
    id: 2024004,
    customerId: 1,
    customerName: "ČEZ Distribuce a.s.",
    date: "2024-01-22",
    status: "planning",
    priority: "low",
    total: 68400,
    deliveryDate: "2024-02-28",
    items: [
      { productId: 1, productName: "Silový kabel CYKY 3x2,5", quantity: 1200, unit: "m", price: 45.50 },
      { productId: 2, productName: "Datový kabel UTP Cat6", quantity: 600, unit: "m", price: 12.80 }
    ]
  }
];

// Nová data pro stroje a výrobu
export const machines = [
  {
    id: "EXT-001",
    name: "Extruder Linka 1",
    type: "Extruder",
    status: "running",
    efficiency: 87,
    temperature: 185,
    speed: 45,
    speedUnit: "m/min",
    currentProduct: "CYKY 3x2,5",
    shift: "Ranní",
    operator: "Jan Novák",
    lastMaintenance: "2024-01-10",
    nextMaintenance: "2024-02-10",
    totalProduced: 12450,
    producedUnit: "m",
    alerts: []
  },
  {
    id: "EXT-002",
    name: "Extruder Linka 2",
    type: "Extruder",
    status: "maintenance",
    efficiency: 0,
    temperature: 25,
    speed: 0,
    speedUnit: "m/min",
    currentProduct: null,
    shift: "Ranní",
    operator: "Údržba",
    lastMaintenance: "2024-01-22",
    nextMaintenance: "2024-02-22",
    totalProduced: 0,
    producedUnit: "m",
    alerts: ["Plánovaná údržba do 14:00"]
  },
  {
    id: "CUT-001",
    name: "Řezací automat 1",
    type: "Cutting",
    status: "running",
    efficiency: 92,
    temperature: null,
    speed: 120,
    speedUnit: "řezů/min",
    currentProduct: "UTP Cat6",
    shift: "Ranní",
    operator: "Marie Svobodová",
    lastMaintenance: "2024-01-05",
    nextMaintenance: "2024-02-05",
    totalProduced: 8750,
    producedUnit: "m",
    alerts: []
  },
  {
    id: "TEST-001",
    name: "Testovací stanice",
    type: "Testing",
    status: "warning",
    efficiency: 78,
    temperature: null,
    speed: 25,
    speedUnit: "testů/h",
    currentProduct: "RG6",
    shift: "Ranní",
    operator: "Pavel Dvořák",
    lastMaintenance: "2024-01-15",
    nextMaintenance: "2024-02-15",
    totalProduced: 2100,
    producedUnit: "m",
    alerts: ["Vysoká chybovost - 3.2%"]
  },
  {
    id: "WIND-001",
    name: "Navíjecí stroj 1",
    type: "Winding",
    status: "idle",
    efficiency: 0,
    temperature: null,
    speed: 0,
    speedUnit: "m/min",
    currentProduct: null,
    shift: "Ranní",
    operator: "Tomáš Procházka",
    lastMaintenance: "2024-01-20",
    nextMaintenance: "2024-02-20",
    totalProduced: 0,
    producedUnit: "m",
    alerts: ["Čeká na materiál"]
  }
];

export const invoices = [
  {
    id: "INV-2024-001",
    orderId: 2024001,
    customerId: 1,
    customerName: "ČEZ Distribuce a.s.",
    issueDate: "2024-01-16",
    dueDate: "2024-02-15",
    status: "paid",
    total: 125000,
    paidDate: "2024-01-25"
  },
  {
    id: "INV-2024-002",
    orderId: 2024002,
    customerId: 2,
    customerName: "Škoda Auto a.s.",
    issueDate: "2024-01-19",
    dueDate: "2024-02-18",
    status: "pending",
    total: 89600,
    paidDate: null
  },
  {
    id: "INV-2024-003",
    orderId: 2024004,
    customerId: 1,
    customerName: "ČEZ Distribuce a.s.",
    issueDate: "2024-01-23",
    dueDate: "2024-02-22",
    status: "pending",
    total: 68400,
    paidDate: null
  }
];

export const dashboardStats = {
  totalRevenue: 6250000,
  totalOrders: 48,
  totalCustomers: 4,
  pendingInvoices: 2,
  activeProduction: 3,
  machinesRunning: 3,
  totalMachines: 5,
  dailyProduction: 23300,
  productionEfficiency: 84.2,
  recentActivity: [
    { type: "machine", message: "Extruder Linka 2 - Údržba dokončena", time: "před 30 min", severity: "info" },
    { type: "production", message: "Dokončena výroba 1200m CYKY 3x2,5", time: "před 1 hodinou", severity: "success" },
    { type: "alert", message: "Testovací stanice - Vysoká chybovost", time: "před 2 hodinami", severity: "warning" },
    { type: "order", message: "Nová objednávka #2024004 od ČEZ Distribuce", time: "před 3 hodinami", severity: "info" },
    { type: "inventory", message: "Nízká zásoba UTP Cat6 - 300m", time: "před 4 hodinami", severity: "warning" },
    { type: "maintenance", message: "Plánovaná údržba Řezací automat 1", time: "včera", severity: "info" }
  ]
};

// Skladové lokace a zásoby
export const inventory = [
  {
    location: "Sklad A",
    zones: [
      { id: "A-1", name: "Silové kabely", capacity: 5000, occupied: 3200, items: 12 },
      { id: "A-2", name: "Koaxiální kabely", capacity: 4000, occupied: 3200, items: 8 },
      { id: "A-3", name: "Speciální kabely", capacity: 2000, occupied: 850, items: 5 }
    ]
  },
  {
    location: "Sklad B",
    zones: [
      { id: "B-1", name: "Řídicí kabely", capacity: 3000, occupied: 1200, items: 15 },
      { id: "B-2", name: "Datové kabely", capacity: 3500, occupied: 1850, items: 10 }
    ]
  },
  {
    location: "Sklad C",
    zones: [
      { id: "C-1", name: "Optické kabely", capacity: 1500, occupied: 850, items: 6 },
      { id: "C-2", name: "Hotové výrobky", capacity: 2000, occupied: 1200, items: 25 }
    ]
  }
];

// Výrobní plán
export const productionPlan = [
  {
    id: "PLAN-001",
    orderId: 2024002,
    product: "Datový kabel UTP Cat6",
    quantity: 1500,
    unit: "m",
    machine: "EXT-001",
    startDate: "2024-01-23",
    endDate: "2024-01-25",
    status: "in_progress",
    progress: 65,
    operator: "Jan Novák"
  },
  {
    id: "PLAN-002",
    orderId: 2024003,
    product: "Optický kabel 24vl SM",
    quantity: 1250,
    unit: "m",
    machine: "EXT-002",
    startDate: "2024-01-24",
    endDate: "2024-01-28",
    status: "scheduled",
    progress: 0,
    operator: "Marie Svobodová"
  },
  {
    id: "PLAN-003",
    orderId: 2024004,
    product: "Silový kabel CYKY 3x2,5",
    quantity: 1200,
    unit: "m",
    machine: "EXT-001",
    startDate: "2024-01-26",
    endDate: "2024-01-27",
    status: "scheduled",
    progress: 0,
    operator: "Jan Novák"
  }
];

// AI Agenti a workflow data
export const aiAgents = [
  {
    id: "webhook-001",
    name: "Webhook Receiver",
    type: "trigger",
    category: "input",
    description: "Příjem HTTP požadavků z externích systémů",
    status: "active",
    lastExecution: "2024-01-23T14:32:00Z",
    executionCount: 1247,
    successRate: 98.5,
    configuration: {
      endpoint: "/api/webhook/orders",
      method: "POST",
      authentication: "bearer_token"
    }
  },
  {
    id: "openai-001",
    name: "OpenAI GPT-4",
    type: "processor",
    category: "ai",
    description: "Generování a analýza textu pomocí GPT-4",
    status: "active",
    lastExecution: "2024-01-23T14:30:00Z",
    executionCount: 892,
    successRate: 99.2,
    configuration: {
      model: "gpt-4",
      temperature: 0.7,
      max_tokens: 1000
    }
  },
  {
    id: "email-001",
    name: "Email Sender",
    type: "action",
    category: "communication",
    description: "Odesílání emailových notifikací",
    status: "active",
    lastExecution: "2024-01-23T14:25:00Z",
    executionCount: 456,
    successRate: 97.8,
    configuration: {
      smtp_server: "smtp.company.com",
      port: 587,
      encryption: "tls"
    }
  },
  {
    id: "mysql-001",
    name: "MySQL Connector",
    type: "processor",
    category: "database",
    description: "Připojení k MySQL databázi pro čtení/zápis dat",
    status: "active",
    lastExecution: "2024-01-23T14:28:00Z",
    executionCount: 2341,
    successRate: 99.8,
    configuration: {
      host: "localhost",
      database: "erp_production",
      connection_pool: 10
    }
  },
  {
    id: "scheduler-001",
    name: "Task Scheduler",
    type: "trigger",
    category: "input",
    description: "Časové spouštění úloh podle cron výrazů",
    status: "active",
    lastExecution: "2024-01-23T14:00:00Z",
    executionCount: 168,
    successRate: 100,
    configuration: {
      cron_expression: "0 */15 * * * *",
      timezone: "Europe/Prague"
    }
  }
];

export const aiWorkflows = [
  {
    id: "workflow-001",
    name: "Automatické zpracování objednávek",
    description: "Workflow pro automatické zpracování příchozích objednávek",
    status: "active",
    created: "2024-01-15T10:00:00Z",
    lastModified: "2024-01-20T16:30:00Z",
    lastExecution: "2024-01-23T14:32:00Z",
    executionCount: 234,
    successRate: 96.8,
    nodes: [
      {
        id: "node-001",
        agentId: "webhook-001",
        position: { x: 100, y: 100 },
        configuration: {
          endpoint: "/api/orders/new"
        }
      },
      {
        id: "node-002",
        agentId: "openai-001",
        position: { x: 350, y: 100 },
        configuration: {
          prompt: "Analyzuj příchozí objednávku a zkontroluj její validitu"
        }
      },
      {
        id: "node-003",
        agentId: "mysql-001",
        position: { x: 600, y: 100 },
        configuration: {
          query: "INSERT INTO orders (customer_id, items, total) VALUES (?, ?, ?)"
        }
      },
      {
        id: "node-004",
        agentId: "email-001",
        position: { x: 850, y: 100 },
        configuration: {
          template: "order_confirmation",
          recipient: "customer_email"
        }
      }
    ],
    connections: [
      { from: "node-001", to: "node-002", type: "success" },
      { from: "node-002", to: "node-003", type: "success" },
      { from: "node-003", to: "node-004", type: "success" }
    ]
  },
  {
    id: "workflow-002",
    name: "Monitoring výroby",
    description: "Sledování stavu výrobních linek a alerting",
    status: "active",
    created: "2024-01-10T09:00:00Z",
    lastModified: "2024-01-18T14:15:00Z",
    lastExecution: "2024-01-23T14:00:00Z",
    executionCount: 672,
    successRate: 99.1,
    nodes: [
      {
        id: "node-101",
        agentId: "scheduler-001",
        position: { x: 100, y: 200 },
        configuration: {
          cron_expression: "0 */5 * * * *"
        }
      },
      {
        id: "node-102",
        agentId: "mysql-001",
        position: { x: 350, y: 200 },
        configuration: {
          query: "SELECT * FROM machines WHERE status = 'error' OR efficiency < 70"
        }
      },
      {
        id: "node-103",
        agentId: "openai-001",
        position: { x: 600, y: 200 },
        configuration: {
          prompt: "Vyhodnoť stav strojů a navrhni doporučení"
        }
      },
      {
        id: "node-104",
        agentId: "email-001",
        position: { x: 850, y: 200 },
        configuration: {
          template: "machine_alert",
          recipient: "<EMAIL>"
        }
      }
    ],
    connections: [
      { from: "node-101", to: "node-102", type: "success" },
      { from: "node-102", to: "node-103", type: "success" },
      { from: "node-103", to: "node-104", type: "success" }
    ]
  },
  {
    id: "workflow-003",
    name: "Analýza zákaznických požadavků",
    description: "AI analýza příchozích požadavků a automatická kategorizace",
    status: "draft",
    created: "2024-01-22T11:30:00Z",
    lastModified: "2024-01-23T09:45:00Z",
    lastExecution: null,
    executionCount: 0,
    successRate: 0,
    nodes: [
      {
        id: "node-201",
        agentId: "webhook-001",
        position: { x: 100, y: 300 },
        configuration: {
          endpoint: "/api/customer/request"
        }
      },
      {
        id: "node-202",
        agentId: "openai-001",
        position: { x: 350, y: 300 },
        configuration: {
          prompt: "Analyzuj zákaznický požadavek a kategorizuj jej podle typu a priority"
        }
      }
    ],
    connections: [
      { from: "node-201", to: "node-202", type: "success" }
    ]
  }
];

export const aiExecutionHistory = [
  {
    id: "exec-001",
    workflowId: "workflow-001",
    startTime: "2024-01-23T14:32:15Z",
    endTime: "2024-01-23T14:32:18Z",
    status: "success",
    duration: 3.2,
    inputData: {
      customer_id: 1,
      items: [{ product: "CYKY 3x2,5", quantity: 500 }],
      total: 22750
    },
    outputData: {
      order_id: 2024005,
      confirmation_sent: true
    },
    nodeExecutions: [
      { nodeId: "node-001", status: "success", duration: 0.1 },
      { nodeId: "node-002", status: "success", duration: 1.8 },
      { nodeId: "node-003", status: "success", duration: 0.3 },
      { nodeId: "node-004", status: "success", duration: 1.0 }
    ]
  },
  {
    id: "exec-002",
    workflowId: "workflow-002",
    startTime: "2024-01-23T14:00:00Z",
    endTime: "2024-01-23T14:00:05Z",
    status: "success",
    duration: 5.1,
    inputData: null,
    outputData: {
      alerts_sent: 1,
      machines_checked: 5
    },
    nodeExecutions: [
      { nodeId: "node-101", status: "success", duration: 0.1 },
      { nodeId: "node-102", status: "success", duration: 2.3 },
      { nodeId: "node-103", status: "success", duration: 1.8 },
      { nodeId: "node-104", status: "success", duration: 0.9 }
    ]
  }
];
