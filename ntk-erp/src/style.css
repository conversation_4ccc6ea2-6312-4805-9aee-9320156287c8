@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    @apply bg-slate-900 text-gray-100 h-full;
  }

  body {
    @apply bg-slate-900 text-gray-100 h-full;
  }

  #app {
    @apply h-full;
  }

  /* Custom Scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1e293b; /* slate-800 */
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #2563eb); /* blue gradient */
    border-radius: 4px;
    border: 1px solid #334155; /* slate-700 */
  }

  ::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8); /* darker blue gradient */
  }

  ::-webkit-scrollbar-thumb:active {
    background: linear-gradient(135deg, #1d4ed8, #1e40af); /* even darker blue gradient */
  }

  ::-webkit-scrollbar-corner {
    background: #1e293b; /* slate-800 */
  }

  /* Firefox scrollbar */
  * {
    scrollbar-width: thin;
    scrollbar-color: #3b82f6 #1e293b; /* thumb track */
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar for specific containers */
  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5); /* semi-transparent slate-800 */
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.8));
    border-radius: 3px;
    transition: all 0.2s ease;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 0.9));
    width: 8px;
  }

  /* Thin scrollbar for sidebar */
  .sidebar-scrollbar::-webkit-scrollbar {
    width: 4px;
  }

  .sidebar-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.3);
    border-radius: 2px;
  }

  .sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
  }
}

@layer components {
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
  }

  .btn-secondary {
    @apply bg-slate-700 hover:bg-slate-600 text-gray-300 hover:text-white font-medium py-2 px-4 rounded-lg transition-all duration-200 border border-slate-600;
  }

  .btn-danger {
    @apply bg-red-600 hover:bg-red-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }

  .btn-success {
    @apply bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-all duration-200;
  }

  .card {
    @apply bg-slate-800 rounded-lg shadow-xl border border-slate-700 p-6 backdrop-blur-sm;
  }

  .sidebar-link {
    @apply flex items-center px-4 py-3 text-gray-400 hover:bg-slate-700 hover:text-blue-400 rounded-lg transition-all duration-200 mx-2;
  }

  .sidebar-link.active {
    @apply bg-blue-600/20 text-blue-400 font-medium border-r-2 border-blue-500;
  }

  .table-header {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider bg-slate-800;
  }

  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-300;
  }

  .status-badge {
    @apply inline-flex px-2 py-1 text-xs font-semibold rounded-full;
  }

  .status-running {
    @apply bg-green-500/20 text-green-400 border border-green-500/30;
  }

  .status-warning {
    @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
  }

  .status-danger {
    @apply bg-red-500/20 text-red-400 border border-red-500/30;
  }

  .status-idle {
    @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
  }

  .status-maintenance {
    @apply bg-blue-500/20 text-blue-400 border border-blue-500/30;
  }

  .metric-card {
    @apply bg-gradient-to-br from-slate-800 to-slate-700 rounded-lg p-6 border border-slate-600 shadow-lg;
  }

  .progress-bar {
    @apply w-full bg-slate-700 rounded-full h-2;
  }

  .progress-fill {
    @apply h-2 rounded-full transition-all duration-300;
  }

  .input-field {
    @apply w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }

  .select-field {
    @apply w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors duration-200;
  }

  /* Table scrollbar */
  .table-scrollbar::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  .table-scrollbar::-webkit-scrollbar-track {
    background: rgba(51, 65, 85, 0.3); /* slate-700 with opacity */
    border-radius: 3px;
  }

  .table-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #1d4ed8);
    border-radius: 3px;
    transition: all 0.3s ease;
  }

  .table-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #1e40af);
    transform: scale(1.1);
  }

  /* Card container scrollbar */
  .card-scrollbar::-webkit-scrollbar {
    width: 5px;
  }

  .card-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }

  .card-scrollbar::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 2.5px;
    transition: all 0.2s ease;
  }

  .card-scrollbar::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.7);
  }

  /* Scrollbar glow effect */
  .glow-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #3b82f6, #2563eb);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(59, 130, 246, 0.5);
    transition: all 0.3s ease;
  }

  .glow-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #2563eb, #1d4ed8);
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.8);
  }

  /* AI Workflow Styles */
  .workflow-node {
    @apply min-w-[200px] bg-slate-700 rounded-xl border-2 border-slate-600 shadow-xl cursor-move transition-all duration-300;
    user-select: none;
    backdrop-filter: blur(8px);
    background: linear-gradient(135deg, rgba(51, 65, 85, 0.9), rgba(30, 41, 59, 0.9));
  }

  .workflow-node:hover {
    @apply border-slate-500;
    transform: translateY(-2px) scale(1.02);
    box-shadow:
      0 10px 25px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(59, 130, 246, 0.1);
  }

  .workflow-node.selected {
    @apply border-blue-500;
    box-shadow:
      0 0 30px rgba(59, 130, 246, 0.4),
      0 10px 25px rgba(0, 0, 0, 0.3);
    transform: translateY(-1px);
  }

  .workflow-node.dragging {
    @apply opacity-90;
    z-index: 1000;
    transform: scale(1.05) rotate(2deg);
    box-shadow:
      0 15px 35px rgba(0, 0, 0, 0.4),
      0 0 25px rgba(59, 130, 246, 0.3);
  }

  .node-header {
    @apply flex items-center space-x-3 p-4 border-b border-slate-600/50;
    background: linear-gradient(135deg, rgba(71, 85, 105, 0.3), rgba(51, 65, 85, 0.3));
    border-radius: 0.75rem 0.75rem 0 0;
  }

  .node-icon {
    @apply w-10 h-10 rounded-lg flex items-center justify-center shadow-lg;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .node-title {
    @apply text-sm font-semibold text-white flex-1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .node-body {
    @apply p-4;
    background: linear-gradient(135deg, rgba(30, 41, 59, 0.5), rgba(15, 23, 42, 0.5));
  }

  .node-play-indicator {
    @apply w-6 h-6 bg-green-500/20 rounded-full flex items-center justify-center ml-auto;
    animation: pulse-green 2s infinite;
  }

  @keyframes pulse-green {
    0%, 100% {
      box-shadow: 0 0 0 0 rgba(16, 185, 129, 0.7);
    }
    70% {
      box-shadow: 0 0 0 6px rgba(16, 185, 129, 0);
    }
  }

  .node-connections {
    @apply relative;
  }

  .connection-point {
    @apply w-4 h-4 rounded-full border-2 border-slate-400 bg-slate-700 absolute cursor-crosshair transition-all duration-300;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  }

  .connection-point:hover {
    @apply border-blue-400 bg-blue-500 scale-125;
    box-shadow:
      0 0 15px rgba(59, 130, 246, 0.6),
      inset 0 1px 2px rgba(255, 255, 255, 0.2);
    transform: scale(1.25);
  }

  .connection-point.input {
    @apply -left-2 top-1/2 transform -translate-y-1/2;
  }

  .connection-point.output {
    @apply -right-2 top-1/2 transform -translate-y-1/2;
  }

  .connection-point.connected {
    @apply border-green-400 bg-green-500;
    box-shadow:
      0 0 12px rgba(16, 185, 129, 0.5),
      inset 0 1px 2px rgba(255, 255, 255, 0.2);
  }

  .connection-point.input::before {
    content: '';
    @apply absolute w-2 h-2 bg-slate-600 rounded-full;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.2s ease;
  }

  .connection-point.output::after {
    content: '';
    @apply absolute w-2 h-2 bg-slate-600 rounded-full;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.2s ease;
  }

  .connection-point:hover::before,
  .connection-point:hover::after {
    @apply bg-white;
    transform: translate(-50%, -50%) scale(0.8);
  }

  .connection-lines {
    @apply pointer-events-none;
    z-index: 1;
  }

  .connection-path {
    transition: all 0.3s ease;
    cursor: pointer;
    pointer-events: stroke;
  }

  .connection-path:hover {
    stroke-width: 4;
    filter: url(#glow) brightness(1.2);
  }

  .active-connection {
    animation: pulse-connection 2s infinite;
  }

  @keyframes pulse-connection {
    0%, 100% {
      stroke-width: 3;
      filter: url(#glow) brightness(1);
    }
    50% {
      stroke-width: 4;
      filter: url(#glow) brightness(1.3);
    }
  }

  /* Enhanced connection styles */
  .connection-creating {
    stroke: #3b82f6;
    stroke-width: 3;
    stroke-dasharray: 8, 4;
    animation: dash-flow 1.5s linear infinite;
    filter: url(#glow);
  }

  @keyframes dash-flow {
    to {
      stroke-dashoffset: -12;
    }
  }

  /* Connection hover effects */
  .workflow-node:hover .connection-point {
    @apply scale-110;
    box-shadow: 0 0 15px rgba(59, 130, 246, 0.6);
  }

  .connection-point.connecting {
    @apply scale-150 border-blue-400 bg-blue-500;
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.8);
    animation: pulse-point 1s infinite;
  }

  @keyframes pulse-point {
    0%, 100% {
      transform: scale(1.5);
      opacity: 1;
    }
    50% {
      transform: scale(1.8);
      opacity: 0.7;
    }
  }

  .agent-node-template {
    @apply transition-all duration-200;
  }

  .agent-node-template:hover {
    @apply transform scale-105;
  }

  .agent-node-template:active {
    @apply transform scale-95;
  }

  .workflow-canvas {
    @apply relative overflow-hidden;
    background-color: #1e293b;
    background-image:
      radial-gradient(circle at 1px 1px, rgba(100, 116, 139, 0.15) 1px, transparent 0);
    background-size: 20px 20px;
  }

  .canvas-grid {
    @apply absolute inset-0 opacity-20;
    background-image:
      linear-gradient(rgba(100, 116, 139, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(100, 116, 139, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  .zoom-controls {
    @apply flex items-center space-x-2 bg-slate-800/90 backdrop-blur-sm rounded-lg border border-slate-600 p-2;
  }

  .zoom-button {
    @apply w-8 h-8 flex items-center justify-center rounded-md bg-slate-700 hover:bg-slate-600 text-gray-300 hover:text-white transition-colors duration-200;
  }

  .properties-panel {
    @apply bg-slate-900 border-l border-slate-700;
  }

  .properties-section {
    @apply space-y-4;
  }

  .property-group {
    @apply space-y-2;
  }

  .property-label {
    @apply block text-sm font-medium text-gray-300;
  }

  .property-input {
    @apply w-full bg-slate-800 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500 transition-colors duration-200;
  }

  .property-textarea {
    @apply w-full bg-slate-800 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500 transition-colors duration-200 resize-none;
  }

  .workflow-toolbar {
    @apply bg-slate-800 border-t border-slate-700 px-4 py-3 flex items-center justify-between;
  }

  .toolbar-section {
    @apply flex items-center space-x-3;
  }

  .status-indicator {
    @apply flex items-center space-x-2 text-sm text-gray-400;
  }

  .status-dot {
    @apply w-2 h-2 rounded-full;
  }

  /* Enhanced Play Button */
  .play-button {
    @apply flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl;
    background: linear-gradient(135deg, #16a34a, #15803d);
  }

  .play-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(22, 163, 74, 0.3);
  }

  .play-button:active {
    transform: translateY(0);
    box-shadow: 0 5px 10px rgba(22, 163, 74, 0.3);
  }

  .play-icon {
    @apply w-8 h-8 bg-white/20 rounded-full flex items-center justify-center;
    backdrop-filter: blur(8px);
  }

  /* Toolbar Button */
  .toolbar-button {
    @apply flex items-center px-3 py-2 bg-slate-700 hover:bg-slate-600 text-gray-300 hover:text-white rounded-lg transition-all duration-200 border border-slate-600;
  }

  .toolbar-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  }

  /* Status dots with glow */
  .status-dot {
    @apply w-2 h-2 rounded-full;
    box-shadow: 0 0 6px currentColor;
  }

  .execution-status {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium;
  }

  .execution-status.success {
    @apply bg-green-500/20 text-green-400;
  }

  .execution-status.error {
    @apply bg-red-500/20 text-red-400;
  }

  .execution-status.running {
    @apply bg-blue-500/20 text-blue-400;
    animation: pulse 2s infinite;
  }

  .agent-category {
    @apply space-y-2;
  }

  .category-header {
    @apply flex items-center space-x-2 text-sm font-medium text-gray-300 mb-3 px-2;
  }

  .category-icon {
    @apply w-4 h-4;
  }

  /* Drag and drop styles */
  .drag-over {
    @apply bg-blue-500/10 border-blue-500/50;
  }

  .drop-zone {
    @apply border-2 border-dashed border-slate-600 rounded-lg p-8 text-center text-gray-400 transition-all duration-200;
  }

  .drop-zone.active {
    @apply border-blue-500 bg-blue-500/5 text-blue-400;
  }

  /* Animation for new nodes */
  .node-appear {
    animation: nodeAppear 0.3s ease-out;
  }

  @keyframes nodeAppear {
    from {
      opacity: 0;
      transform: scale(0.8) translateY(-10px);
    }
    to {
      opacity: 1;
      transform: scale(1) translateY(0);
    }
  }

  /* Connection animation */
  .connection-creating {
    @apply stroke-blue-400;
    stroke-width: 2;
    stroke-dasharray: 5, 5;
    animation: dash 1s linear infinite;
  }

  @keyframes dash {
    to {
      stroke-dashoffset: -10;
    }
  }
}
