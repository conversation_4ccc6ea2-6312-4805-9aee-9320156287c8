import { orders } from '../data/mockData.js'

export function renderOrders() {
  return `
    <div class="space-y-6">
      <!-- Header with Actions -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-white"><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></h2>
          <p class="text-gray-400">Správa objednávek a jejich stavu</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
            </svg>
            Filtrovat
          </button>
          <button class="btn-primary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nová objednávka
          </button>
        </div>
      </div>
      
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">Celkem</p>
              <p class="text-2xl font-semibold text-white">${orders.length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">Čekající</p>
              <p class="text-2xl font-semibold text-white">${orders.filter(o => o.status === 'pending').length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">Zpracovávané</p>
              <p class="text-2xl font-semibold text-white">${orders.filter(o => o.status === 'processing').length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-400">Dokončené</p>
              <p class="text-2xl font-semibold text-white">${orders.filter(o => o.status === 'completed').length}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Search and Filters -->
      <div class="card">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div class="flex-1 max-w-md">
            <div class="relative">
              <input type="text" placeholder="Hledat objednávky..."
                     class="input-field pl-10 pr-4">
              <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex space-x-3">
            <select class="select-field">
              <option>Všechny stavy</option>
              <option>Čekající</option>
              <option>Zpracovávané</option>
              <option>Dokončené</option>
            </select>
            <input type="date" class="input-field">
          </div>
        </div>
      </div>
      
      <!-- Orders Table -->
      <div class="card p-0">
        <div class="overflow-x-auto table-scrollbar">
          <table class="min-w-full divide-y divide-slate-700">
            <thead class="bg-slate-800">
              <tr>
                <th class="table-header">Číslo objednávky</th>
                <th class="table-header">Zákazník</th>
                <th class="table-header">Datum</th>
                <th class="table-header">Stav</th>
                <th class="table-header">Položky</th>
                <th class="table-header">Celková částka</th>
                <th class="table-header">Akce</th>
              </tr>
            </thead>
            <tbody class="bg-slate-800 divide-y divide-slate-700">
              ${orders.map(order => `
                <tr class="hover:bg-slate-700 transition-colors duration-200">
                  <td class="table-cell">
                    <div class="text-sm font-medium text-white">#${order.id}</div>
                  </td>
                  <td class="table-cell">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        ${order.customerName.charAt(0)}
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-white">${order.customerName}</div>
                        <div class="text-sm text-gray-400">ID: ${order.customerId}</div>
                      </div>
                    </div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-300">${new Date(order.date).toLocaleDateString('cs-CZ')}</div>
                  </td>
                  <td class="table-cell">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}">
                      ${getStatusText(order.status)}
                    </span>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-300">${order.items.length} položek</div>
                    <div class="text-xs text-gray-400">
                      ${order.items.slice(0, 2).map(item => item.productName).join(', ')}
                      ${order.items.length > 2 ? '...' : ''}
                    </div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm font-medium text-white">${order.total.toLocaleString('cs-CZ')} Kč</div>
                  </td>
                  <td class="table-cell">
                    <div class="flex space-x-2">
                      <button class="text-blue-400 hover:text-blue-300 text-sm font-medium">
                        Detail
                      </button>
                      <button class="text-gray-400 hover:text-gray-300 text-sm font-medium">
                        Upravit
                      </button>
                    </div>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  `
}

function getStatusColor(status) {
  const colors = {
    pending: 'status-warning',
    processing: 'bg-orange-500/20 text-orange-400 border border-orange-500/30',
    completed: 'status-running'
  }
  return colors[status] || 'status-idle'
}

function getStatusText(status) {
  const texts = {
    pending: 'Čekající',
    processing: 'Zpracovává se',
    completed: 'Dokončeno',
    production: 'Ve výrobě',
    planning: 'Plánování'
  }
  return texts[status] || status
}
