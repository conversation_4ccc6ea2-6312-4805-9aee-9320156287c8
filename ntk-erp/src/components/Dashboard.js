import { dashboardStats, machines, products } from '../data/mockData.js'

export function renderDashboard() {
  const { totalRevenue, totalOrders, totalCustomers, pendingInvoices, activeProduction, machinesRunning, totalMachines, dailyProduction, productionEfficiency, recentActivity } = dashboardStats
  
  return `
    <div class="space-y-6">
      <!-- Production Overview -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Denní výroba</p>
              <p class="text-2xl font-bold text-blue-400">${dailyProduction.toLocaleString('cs-CZ')} m</p>
              <p class="text-xs text-green-400 mt-1">+12% oproti včera</p>
            </div>
            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Aktivní stroje</p>
              <p class="text-2xl font-bold text-green-400">${machinesRunning}/${totalMachines}</p>
              <p class="text-xs text-gray-400 mt-1">Efektivita ${productionEfficiency}%</p>
            </div>
            <div class="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Aktivní výroba</p>
              <p class="text-2xl font-bold text-yellow-400">${activeProduction}</p>
              <p class="text-xs text-gray-400 mt-1">Výrobních úkolů</p>
            </div>
            <div class="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
              </svg>
            </div>
          </div>
        </div>

        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Měsíční tržby</p>
              <p class="text-2xl font-bold text-purple-400">${(totalRevenue/1000000).toFixed(1)}M Kč</p>
              <p class="text-xs text-success-400 mt-1">+8% oproti minulému měsíci</p>
            </div>
            <div class="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Machine Status and Production -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Machine Status -->
        <div class="card">
          <h3 class="text-lg font-semibold text-white mb-4">Stav výrobních linek</h3>
          <div class="space-y-3">
            ${machines.slice(0, 4).map(machine => `
              <div class="flex items-center justify-between p-3 bg-dark-700 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 rounded-full ${getMachineStatusColor(machine.status)}"></div>
                  <div>
                    <p class="text-white font-medium">${machine.name}</p>
                    <p class="text-gray-400 text-sm">${machine.currentProduct || 'Nečinný'}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-white font-medium">${machine.efficiency}%</p>
                  <p class="text-gray-400 text-sm">Efektivita</p>
                </div>
              </div>
            `).join('')}
          </div>
          <div class="mt-4 pt-4 border-t border-dark-700">
            <a href="/machines" data-route="/machines" class="text-sm text-primary-400 hover:text-primary-300 font-medium">
              Zobrazit všechny stroje →
            </a>
          </div>
        </div>

        <!-- Recent Activity -->
        <div class="card">
          <h3 class="text-lg font-semibold text-white mb-4">Poslední aktivita</h3>
          <div class="space-y-4">
            ${recentActivity.map(activity => `
              <div class="flex items-start space-x-3">
                <div class="flex-shrink-0">
                  ${getActivityIcon(activity.type, activity.severity)}
                </div>
                <div class="flex-1 min-w-0">
                  <p class="text-sm text-gray-300">${activity.message}</p>
                  <p class="text-xs text-gray-500">${activity.time}</p>
                </div>
              </div>
            `).join('')}
          </div>
          <div class="mt-4 pt-4 border-t border-dark-700">
            <a href="/production" data-route="/production" class="text-sm text-primary-400 hover:text-primary-300 font-medium">
              Zobrazit všechny aktivity →
            </a>
          </div>
        </div>
      </div>

      <!-- Stock Alerts and Quick Actions -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Stock Alerts -->
        <div class="card">
          <h3 class="text-lg font-semibold text-white mb-4">Skladové upozornění</h3>
          <div class="space-y-3">
            ${products.filter(p => p.stock <= p.minStock).map(product => `
              <div class="flex items-center justify-between p-3 bg-danger-500/10 border border-danger-500/30 rounded-lg">
                <div>
                  <p class="text-danger-400 font-medium">${product.name}</p>
                  <p class="text-gray-400 text-sm">Skladem: ${product.stock} ${product.stockUnit}</p>
                </div>
                <div class="text-right">
                  <span class="status-badge status-danger">Nízké</span>
                </div>
              </div>
            `).join('')}
            ${products.filter(p => p.stock <= p.minStock).length === 0 ? `
              <div class="text-center py-4">
                <svg class="w-12 h-12 text-success-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <p class="text-gray-400">Všechny zásoby jsou dostačující</p>
              </div>
            ` : ''}
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="card">
          <h3 class="text-lg font-semibold text-white mb-4">Rychlé akce</h3>
          <div class="grid grid-cols-1 gap-3">
            <button class="btn-primary text-left flex items-center">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              Nový výrobní plán
            </button>
            <button class="btn-secondary text-left flex items-center">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
              </svg>
              Příjem materiálu
            </button>
            <button class="btn-secondary text-left flex items-center">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
              </svg>
              Spustit inventuru
            </button>
            <button class="btn-secondary text-left flex items-center">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Plánovaná údržba
            </button>
          </div>
        </div>
      </div>
    </div>
  `
}

function getMachineStatusColor(status) {
  const colors = {
    running: 'bg-success-500 animate-pulse',
    maintenance: 'bg-blue-500',
    warning: 'bg-warning-500',
    idle: 'bg-gray-500',
    error: 'bg-danger-500'
  }
  return colors[status] || 'bg-gray-500'
}

function getActivityIcon(type, severity = 'info') {
  const severityColors = {
    success: 'bg-success-500/20 text-success-400',
    warning: 'bg-warning-500/20 text-warning-400',
    danger: 'bg-danger-500/20 text-danger-400',
    info: 'bg-primary-500/20 text-primary-400'
  }

  const colorClass = severityColors[severity] || severityColors.info

  const icons = {
    machine: `<div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
              </div>`,
    production: `<div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
                   <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                     <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
                   </svg>
                 </div>`,
    alert: `<div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>`,
    order: `<div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>`,
    inventory: `<div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                  </svg>
                </div>`,
    maintenance: `<div class="w-8 h-8 ${colorClass} rounded-full flex items-center justify-center">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    </svg>
                  </div>`
  }

  return icons[type] || icons.order
}
