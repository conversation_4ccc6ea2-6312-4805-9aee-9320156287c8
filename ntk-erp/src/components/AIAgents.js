import { aiAgents, aiWorkflows } from '../data/mockData.js'

export function renderAIAgents() {
  return `
    <div class="h-full flex flex-col">
      <!-- Header with controls -->
      <div class="flex items-center justify-between mb-6">
        <div class="flex items-center space-x-4">
          <h2 class="text-2xl font-bold text-white">AI Agenti</h2>
          <div class="flex items-center space-x-2">
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
            <span class="text-sm text-gray-400">3 aktivní agenti</span>
          </div>
        </div>
        <div class="flex items-center space-x-3">
          <button class="btn-secondary" onclick="toggleAgentsList()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
            </svg>
            Seznam agentů
          </button>
          <button class="btn-primary" onclick="createNewWorkflow()">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nový workflow
          </button>
        </div>
      </div>

      <!-- Main workflow area -->
      <div class="flex-1 flex bg-slate-800 rounded-lg border border-slate-700 overflow-hidden">
        <!-- Agents sidebar -->
        <div id="agents-sidebar" class="w-80 bg-slate-900 border-r border-slate-700 flex flex-col">
          <!-- Sidebar header -->
          <div class="p-4 border-b border-slate-700">
            <h3 class="text-lg font-semibold text-white mb-3">Dostupní agenti</h3>
            <div class="relative">
              <input type="text" placeholder="Hledat agenty..." 
                     class="w-full bg-slate-800 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500">
              <svg class="w-4 h-4 text-gray-400 absolute right-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>

          <!-- Agent categories -->
          <div class="flex-1 overflow-y-auto p-4 space-y-4">
            ${renderAgentCategories()}
          </div>
        </div>

        <!-- Workflow canvas -->
        <div class="flex-1 relative bg-slate-800" id="workflow-canvas">
          <!-- Canvas background pattern -->
          <div class="absolute inset-0 opacity-10" style="background-image: radial-gradient(circle, #64748b 1px, transparent 1px); background-size: 20px 20px;"></div>
          
          <!-- Canvas content -->
          <div class="relative h-full" id="canvas-content">
            ${renderWorkflowCanvas()}
          </div>

          <!-- Canvas controls -->
          <div class="absolute bottom-4 right-4 flex items-center space-x-2">
            <button class="bg-slate-700 hover:bg-slate-600 text-white p-2 rounded-lg border border-slate-600" onclick="zoomOut()">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"></path>
              </svg>
            </button>
            <span class="text-sm text-gray-400 px-2">100%</span>
            <button class="bg-slate-700 hover:bg-slate-600 text-white p-2 rounded-lg border border-slate-600" onclick="zoomIn()">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </button>
            <button class="bg-slate-700 hover:bg-slate-600 text-white p-2 rounded-lg border border-slate-600" onclick="fitToScreen()">
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- Properties panel -->
        <div id="properties-panel" class="w-80 bg-slate-900 border-l border-slate-700 flex flex-col" style="display: none;">
          <div class="p-4 border-b border-slate-700">
            <h3 class="text-lg font-semibold text-white">Vlastnosti</h3>
          </div>
          <div class="flex-1 overflow-y-auto p-4" id="properties-content">
            <p class="text-gray-400 text-sm">Vyberte uzel pro zobrazení vlastností</p>
          </div>
        </div>
      </div>

      <!-- Bottom toolbar -->
      <div class="mt-4 flex items-center justify-between bg-slate-800 rounded-lg border border-slate-700 px-4 py-3">
        <div class="flex items-center space-x-3">
          <!-- Green Play Button -->
          <button class="play-button" onclick="executeWorkflow()">
            <div class="play-icon">
              <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z"/>
              </svg>
            </div>
            <span class="ml-2 text-sm font-medium text-white">Spustit workflow</span>
          </button>

          <!-- Save Button -->
          <button class="toolbar-button" onclick="saveWorkflow()">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12"></path>
            </svg>
            <span class="ml-2 text-sm">Uložit</span>
          </button>
        </div>

        <div class="flex items-center space-x-6 text-sm text-gray-400">
          <div class="flex items-center space-x-2">
            <div class="status-dot bg-blue-500"></div>
            <span>5 uzlů</span>
          </div>
          <div class="flex items-center space-x-2">
            <div class="status-dot bg-green-500"></div>
            <span>3 připojení</span>
          </div>
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>Poslední spuštění: 14:32</span>
          </div>
        </div>
      </div>
    </div>

    <script>
      ${getAIAgentsScript()}
    </script>
  `
}

function renderAgentCategories() {
  const categories = [
    {
      name: 'Vstupní uzly',
      icon: 'M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z',
      agents: [
        { name: 'Webhook', description: 'Příjem HTTP požadavků', color: 'bg-blue-500' },
        { name: 'Scheduler', description: 'Časové spouštění', color: 'bg-green-500' },
        { name: 'File Watcher', description: 'Sledování souborů', color: 'bg-yellow-500' }
      ]
    },
    {
      name: 'AI Models',
      icon: 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z',
      agents: [
        { name: 'OpenAI GPT', description: 'Generování textu', color: 'bg-purple-500' },
        { name: 'Ollama', description: 'Analýza sentimentu', color: 'bg-indigo-500' }
      ]
    },
    {
      name: 'Databáze',
      icon: 'M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4',
      agents: [
        { name: 'MySQL', description: 'MySQL databáze', color: 'bg-orange-500' },
        { name: 'PostgreSQL', description: 'PostgreSQL databáze', color: 'bg-blue-600' },
        { name: 'MongoDB', description: 'MongoDB databáze', color: 'bg-green-600' }
      ]
    },
    {
      name: 'Komunikace',
      icon: 'M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z',
      agents: [
        { name: 'Email', description: 'Odesílání emailů', color: 'bg-red-500' },
        { name: 'Slack', description: 'Slack notifikace', color: 'bg-purple-600' },
        { name: 'Teams', description: 'Microsoft Teams', color: 'bg-blue-700' }
      ]
    }
  ]

  return categories.map(category => `
    <div class="space-y-2">
      <div class="flex items-center space-x-2 text-sm font-medium text-gray-300 mb-3">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${category.icon}"></path>
        </svg>
        <span>${category.name}</span>
      </div>
      ${category.agents.map(agent => `
        <div class="agent-node-template p-3 bg-slate-800 rounded-lg border border-slate-600 hover:border-slate-500 cursor-grab transition-colors"
             draggable="true" 
             data-agent-type="${agent.name.toLowerCase().replace(/\s+/g, '-')}"
             data-agent-name="${agent.name}"
             data-agent-color="${agent.color}">
          <div class="flex items-center space-x-3">
            <div class="w-8 h-8 ${agent.color} rounded-lg flex items-center justify-center">
              <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="${category.icon}"></path>
              </svg>
            </div>
            <div class="flex-1 min-w-0">
              <p class="text-sm font-medium text-white">${agent.name}</p>
              <p class="text-xs text-gray-400 truncate">${agent.description}</p>
            </div>
          </div>
        </div>
      `).join('')}
    </div>
  `).join('')
}

function renderWorkflowCanvas() {
  return `
    <div class="workflow-nodes" id="workflow-nodes">
      <!-- Example workflow nodes -->
      <div class="workflow-node bg-blue-500" style="position: absolute; top: 100px; left: 200px;" data-node-id="webhook-1">
        <div class="node-header">
          <div class="node-icon">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5"></path>
            </svg>
          </div>
          <span class="node-title">Webhook</span>
          <!-- Green play indicator -->
          <div class="node-play-indicator">
            <svg class="w-3 h-3 text-green-400" fill="currentColor" viewBox="0 0 24 24">
              <path d="M8 5v14l11-7z"/>
            </svg>
          </div>
        </div>
        <div class="node-body">
          <p class="text-xs text-blue-100">Příjem HTTP požadavků</p>
        </div>
        <div class="node-connections">
          <div class="connection-point output" data-connection="output"></div>
        </div>
      </div>

      <div class="workflow-node bg-purple-500" style="position: absolute; top: 100px; left: 450px;" data-node-id="openai-1">
        <div class="node-header">
          <div class="node-icon">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          <span class="node-title">OpenAI GPT</span>
        </div>
        <div class="node-body">
          <p class="text-xs text-purple-100">Generování textu</p>
        </div>
        <div class="node-connections">
          <div class="connection-point input" data-connection="input"></div>
          <div class="connection-point output" data-connection="output"></div>
        </div>
      </div>

      <div class="workflow-node bg-red-500" style="position: absolute; top: 100px; left: 700px;" data-node-id="email-1">
        <div class="node-header">
          <div class="node-icon">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          <span class="node-title">Email</span>
        </div>
        <div class="node-body">
          <p class="text-xs text-red-100">Odesílání emailů</p>
        </div>
        <div class="node-connections">
          <div class="connection-point input" data-connection="input"></div>
        </div>
      </div>
    </div>

    <!-- Connection lines -->
    <svg class="connection-lines" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none;">
      <defs>
        <!-- Enhanced arrow marker -->
        <marker id="arrowhead" markerWidth="12" markerHeight="8" refX="11" refY="4" orient="auto" markerUnits="strokeWidth">
          <path d="M0,0 L0,8 L12,4 z" fill="#64748b" stroke="none"/>
        </marker>

        <!-- Green animated arrow marker -->
        <marker id="arrowhead-active" markerWidth="14" markerHeight="10" refX="13" refY="5" orient="auto" markerUnits="strokeWidth">
          <path d="M0,0 L0,10 L14,5 z" fill="#10b981" stroke="none">
            <animate attributeName="fill" values="#10b981;#34d399;#6ee7b7;#10b981" dur="1.5s" repeatCount="indefinite"/>
          </path>
          <path d="M0,0 L0,10 L14,5 z" fill="url(#greenGlow)" opacity="0.6">
            <animate attributeName="opacity" values="0.6;1;0.6" dur="1.5s" repeatCount="indefinite"/>
          </path>
        </marker>

        <!-- Gradient for connections -->
        <linearGradient id="connectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#64748b;stop-opacity:0.8"/>
          <stop offset="100%" style="stop-color:#94a3b8;stop-opacity:1"/>
        </linearGradient>

        <!-- Active connection gradient -->
        <linearGradient id="activeConnectionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8"/>
          <stop offset="50%" style="stop-color:#34d399;stop-opacity:1"/>
          <stop offset="100%" style="stop-color:#6ee7b7;stop-opacity:0.9"/>
        </linearGradient>

        <!-- Green glow gradient -->
        <radialGradient id="greenGlow" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#10b981;stop-opacity:1"/>
          <stop offset="100%" style="stop-color:#10b981;stop-opacity:0"/>
        </radialGradient>

        <!-- Glow filter -->
        <filter id="glow">
          <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>

      <!-- Example connections with smooth Bezier curves -->
      <path d="M 320 125 C 385 125, 365 125, 430 125"
            stroke="url(#connectionGradient)"
            stroke-width="3"
            fill="none"
            marker-end="url(#arrowhead)"
            class="connection-path"
            filter="url(#glow)"/>

      <path d="M 570 125 C 635 125, 615 125, 680 125"
            stroke="url(#activeConnectionGradient)"
            stroke-width="4"
            fill="none"
            marker-end="url(#arrowhead-active)"
            class="connection-path active-connection"
            filter="url(#glow)">
        <!-- Enhanced flowing animation -->
        <animate attributeName="stroke-width" values="3;5;3" dur="2s" repeatCount="indefinite"/>
        <animate attributeName="stroke-dasharray" values="0,1000;40,960;0,1000" dur="3s" repeatCount="indefinite"/>
        <animate attributeName="stroke-dashoffset" values="0;-1000" dur="3s" repeatCount="indefinite"/>
      </path>

      <!-- Additional example connection with curve -->
      <path d="M 320 160 C 385 180, 365 140, 430 160"
            stroke="url(#connectionGradient)"
            stroke-width="2"
            fill="none"
            marker-end="url(#arrowhead)"
            class="connection-path"
            opacity="0.6"/>

      <!-- Connection with different curve style -->
      <path d="M 570 160 C 600 200, 650 80, 680 160"
            stroke="#64748b"
            stroke-width="2"
            fill="none"
            marker-end="url(#arrowhead)"
            class="connection-path"
            opacity="0.4"
            stroke-dasharray="5,5"/>
    </svg>
  `
}

function getAIAgentsScript() {
  return `
    let selectedNode = null;
    let isDragging = false;
    let dragOffset = { x: 0, y: 0 };

    // Initialize drag and drop functionality
    document.addEventListener('DOMContentLoaded', function() {
      initializeDragAndDrop();
      initializeNodeInteractions();
    });

    function initializeDragAndDrop() {
      const agentTemplates = document.querySelectorAll('.agent-node-template');
      const canvas = document.getElementById('canvas-content');

      agentTemplates.forEach(template => {
        template.addEventListener('dragstart', function(e) {
          e.dataTransfer.setData('text/plain', JSON.stringify({
            type: this.dataset.agentType,
            name: this.dataset.agentName,
            color: this.dataset.agentColor
          }));
        });
      });

      canvas.addEventListener('dragover', function(e) {
        e.preventDefault();
      });

      canvas.addEventListener('drop', function(e) {
        e.preventDefault();
        const data = JSON.parse(e.dataTransfer.getData('text/plain'));
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        createWorkflowNode(data, x, y);
      });
    }

    function createWorkflowNode(agentData, x, y) {
      const nodeId = agentData.type + '-' + Date.now();
      const node = document.createElement('div');
      node.className = 'workflow-node ' + agentData.color + ' node-appear';
      node.style.position = 'absolute';
      node.style.left = x + 'px';
      node.style.top = y + 'px';
      node.dataset.nodeId = nodeId;

      node.innerHTML = \`
        <div class="node-header">
          <div class="node-icon">
            <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
            </svg>
          </div>
          <span class="node-title">\${agentData.name}</span>
        </div>
        <div class="node-body">
          <p class="text-xs opacity-75">Nový uzel</p>
        </div>
        <div class="node-connections">
          <div class="connection-point input" data-connection="input"></div>
          <div class="connection-point output" data-connection="output"></div>
        </div>
      \`;

      document.getElementById('workflow-nodes').appendChild(node);
      initializeNodeInteractions();
      updateConnections();
    }

    function createBezierPath(x1, y1, x2, y2) {
      const dx = x2 - x1;
      const dy = y2 - y1;

      // Calculate control points for smooth curves
      const cp1x = x1 + Math.min(Math.abs(dx) * 0.5, 100);
      const cp1y = y1;
      const cp2x = x2 - Math.min(Math.abs(dx) * 0.5, 100);
      const cp2y = y2;

      return \`M \${x1} \${y1} C \${cp1x} \${cp1y}, \${cp2x} \${cp2y}, \${x2} \${y2}\`;
    }

    function updateConnections() {
      // This would update all connection lines when nodes move
      // For now, we'll keep the static examples
      console.log('Updating connections...');
    }

    function initializeNodeInteractions() {
      const nodes = document.querySelectorAll('.workflow-node');

      nodes.forEach(node => {
        node.addEventListener('mousedown', function(e) {
          if (e.target.classList.contains('connection-point')) return;

          selectedNode = this;
          isDragging = true;

          const rect = this.getBoundingClientRect();
          dragOffset.x = e.clientX - rect.left;
          dragOffset.y = e.clientY - rect.top;

          this.style.zIndex = '1000';
          showNodeProperties(this);
        });

        node.addEventListener('click', function(e) {
          e.stopPropagation();
          selectNode(this);
        });
      });

      document.addEventListener('mousemove', function(e) {
        if (isDragging && selectedNode) {
          const canvas = document.getElementById('canvas-content');
          const canvasRect = canvas.getBoundingClientRect();

          const x = e.clientX - canvasRect.left - dragOffset.x;
          const y = e.clientY - canvasRect.top - dragOffset.y;

          selectedNode.style.left = Math.max(0, x) + 'px';
          selectedNode.style.top = Math.max(0, y) + 'px';
        }
      });

      document.addEventListener('mouseup', function() {
        if (isDragging && selectedNode) {
          selectedNode.style.zIndex = '';
          isDragging = false;
          selectedNode = null;
        }
      });
    }

    function selectNode(node) {
      // Remove selection from other nodes
      document.querySelectorAll('.workflow-node').forEach(n => {
        n.classList.remove('selected');
      });

      // Select current node
      node.classList.add('selected');
      showNodeProperties(node);
    }

    function showNodeProperties(node) {
      const panel = document.getElementById('properties-panel');
      const content = document.getElementById('properties-content');

      panel.style.display = 'flex';

      const nodeTitle = node.querySelector('.node-title').textContent;
      const nodeId = node.dataset.nodeId;

      content.innerHTML = \`
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Název uzlu</label>
            <input type="text" value="\${nodeTitle}" class="w-full bg-slate-800 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">ID uzlu</label>
            <input type="text" value="\${nodeId}" readonly class="w-full bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-gray-400 text-sm">
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Popis</label>
            <textarea class="w-full bg-slate-800 border border-slate-600 rounded-lg px-3 py-2 text-white text-sm focus:outline-none focus:border-blue-500 h-20" placeholder="Popis funkce uzlu..."></textarea>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-300 mb-2">Konfigurace</label>
            <div class="space-y-2">
              <button class="w-full btn-secondary text-sm">Upravit konfiguraci</button>
              <button class="w-full btn-danger text-sm">Smazat uzel</button>
            </div>
          </div>
        </div>
      \`;
    }

    // Toolbar functions
    function toggleAgentsList() {
      const sidebar = document.getElementById('agents-sidebar');
      sidebar.style.display = sidebar.style.display === 'none' ? 'flex' : 'none';
    }

    function createNewWorkflow() {
      if (confirm('Vytvořit nový workflow? Současný workflow bude ztracen.')) {
        document.getElementById('workflow-nodes').innerHTML = '';
        document.getElementById('properties-panel').style.display = 'none';
      }
    }

    function executeWorkflow() {
      alert('Spouštění workflow... (demo funkce)');
    }

    function saveWorkflow() {
      alert('Workflow uložen! (demo funkce)');
    }

    function zoomIn() {
      // Zoom functionality would be implemented here
      console.log('Zoom in');
    }

    function zoomOut() {
      // Zoom functionality would be implemented here
      console.log('Zoom out');
    }

    function fitToScreen() {
      // Fit to screen functionality would be implemented here
      console.log('Fit to screen');
    }

    // Click outside to deselect
    document.addEventListener('click', function(e) {
      if (!e.target.closest('.workflow-node') && !e.target.closest('#properties-panel')) {
        document.querySelectorAll('.workflow-node').forEach(n => {
          n.classList.remove('selected');
        });
        document.getElementById('properties-panel').style.display = 'none';
      }
    });
  `;
}
