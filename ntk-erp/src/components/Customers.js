import { customers } from '../data/mockData.js'

export function renderCustomers() {
  // Add event listeners after render
  setTimeout(() => {
    addCustomerEventListeners()
  }, 100)

  return `
    <div class="space-y-6">
      <!-- Header with Actions -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Z<PERSON>azníci</h2>
          <p class="text-gray-600">Spr<PERSON>va zákaznick<PERSON> databáze</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Import
          </button>
          <button class="btn-primary" data-action="add-customer">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Přidat zákazníka
          </button>
        </div>
      </div>
      
      <!-- Search and Filters -->
      <div class="card">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div class="flex-1 max-w-md">
            <div class="relative">
              <input type="text" placeholder="Hledat zákazníky..."
                     class="input-field pl-10 pr-4" id="customer-search">
              <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex space-x-3">
            <select class="select-field" id="status-filter">
              <option value="">Všechny stavy</option>
              <option value="active">Aktivní</option>
              <option value="inactive">Neaktivní</option>
            </select>
            <select class="select-field" id="sort-filter">
              <option value="">Seřadit podle</option>
              <option value="name-asc">Název A-Z</option>
              <option value="name-desc">Název Z-A</option>
              <option value="date">Datum vytvoření</option>
              <option value="value">Celková hodnota</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- Customers Table -->
      <div class="card p-0">
        <div class="overflow-x-auto table-scrollbar">
          <table class="min-w-full divide-y divide-slate-700" id="customers-table">
            <thead class="bg-slate-800">
              <tr>
                <th class="table-header">
                  <input type="checkbox" class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
                </th>
                <th class="table-header">Zákazník</th>
                <th class="table-header">Kontakt</th>
                <th class="table-header">Adresa</th>
                <th class="table-header">Stav</th>
                <th class="table-header">Objednávky</th>
                <th class="table-header">Celková hodnota</th>
                <th class="table-header">Akce</th>
              </tr>
            </thead>
            <tbody class="bg-slate-800 divide-y divide-slate-700">
              ${customers.map(customer => `
                <tr class="hover:bg-slate-700 transition-colors duration-200">
                  <td class="table-cell">
                    <input type="checkbox" class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
                  </td>
                  <td class="table-cell">
                    <div class="flex items-center">
                      <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
                        ${customer.name.charAt(0)}
                      </div>
                      <div class="ml-4">
                        <div class="text-sm font-medium text-white">${customer.name}</div>
                        <div class="text-sm text-gray-400">ID: ${customer.id}</div>
                      </div>
                    </div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-300">${customer.email}</div>
                    <div class="text-sm text-gray-400">${customer.phone}</div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-300">${customer.address}</div>
                  </td>
                  <td class="table-cell">
                    <span class="status-badge ${customer.status === 'active' ? 'status-running' : 'status-idle'}">
                      ${customer.status === 'active' ? 'Aktivní' : 'Neaktivní'}
                    </span>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-300">${customer.totalOrders}</div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm font-medium text-white">${customer.totalValue.toLocaleString('cs-CZ')} Kč</div>
                  </td>
                  <td class="table-cell">
                    <div class="flex space-x-2">
                      <button class="text-blue-400 hover:text-blue-300 text-sm font-medium" onclick="editCustomer(${customer.id})">
                        Upravit
                      </button>
                      <button class="text-gray-400 hover:text-gray-300 text-sm font-medium" onclick="viewCustomer(${customer.id})">
                        Detail
                      </button>
                    </div>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
        
        <!-- Pagination -->
        <div class="bg-slate-800 px-6 py-3 border-t border-slate-700 flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button class="btn-secondary">
              Předchozí
            </button>
            <button class="btn-secondary">
              Další
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-400">
                Zobrazeno <span class="font-medium text-white">1</span> až <span class="font-medium text-white">${customers.length}</span> z
                <span class="font-medium text-white">${customers.length}</span> výsledků
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                <button class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-slate-600 bg-slate-700 text-sm font-medium text-gray-400 hover:bg-slate-600">
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
                <button class="relative inline-flex items-center px-4 py-2 border border-slate-600 bg-blue-600 text-sm font-medium text-white">
                  1
                </button>
                <button class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-slate-600 bg-slate-700 text-sm font-medium text-gray-400 hover:bg-slate-600">
                  <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                  </svg>
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
}

function addCustomerEventListeners() {
  // Search functionality
  const searchInput = document.getElementById('customer-search')
  const statusFilter = document.getElementById('status-filter')
  const sortFilter = document.getElementById('sort-filter')

  if (searchInput) {
    searchInput.addEventListener('input', (e) => {
      filterCustomers()
    })
  }

  if (statusFilter) {
    statusFilter.addEventListener('change', (e) => {
      filterCustomers()
    })
  }

  if (sortFilter) {
    sortFilter.addEventListener('change', (e) => {
      filterCustomers()
    })
  }

  // Add customer button
  const addCustomerBtn = document.querySelector('[data-action="add-customer"]')
  if (addCustomerBtn) {
    addCustomerBtn.addEventListener('click', () => {
      showAddCustomerModal()
    })
  }
}

function filterCustomers() {
  const searchTerm = document.getElementById('customer-search')?.value.toLowerCase() || ''
  const statusFilter = document.getElementById('status-filter')?.value || ''
  const sortFilter = document.getElementById('sort-filter')?.value || ''

  let filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm) ||
                         customer.email.toLowerCase().includes(searchTerm) ||
                         customer.phone.includes(searchTerm)

    const matchesStatus = !statusFilter || customer.status === statusFilter

    return matchesSearch && matchesStatus
  })

  // Sort customers
  if (sortFilter) {
    switch (sortFilter) {
      case 'name-asc':
        filteredCustomers.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'name-desc':
        filteredCustomers.sort((a, b) => b.name.localeCompare(a.name))
        break
      case 'value':
        filteredCustomers.sort((a, b) => b.totalValue - a.totalValue)
        break
    }
  }

  updateCustomerTable(filteredCustomers)
}

function updateCustomerTable(filteredCustomers) {
  const tbody = document.querySelector('#customers-table tbody')
  if (!tbody) return

  tbody.innerHTML = filteredCustomers.map(customer => `
    <tr class="hover:bg-slate-700 transition-colors duration-200">
      <td class="table-cell">
        <input type="checkbox" class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
      </td>
      <td class="table-cell">
        <div class="flex items-center">
          <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-medium">
            ${customer.name.charAt(0)}
          </div>
          <div class="ml-4">
            <div class="text-sm font-medium text-white">${customer.name}</div>
            <div class="text-sm text-gray-400">ID: ${customer.id}</div>
          </div>
        </div>
      </td>
      <td class="table-cell">
        <div class="text-sm text-gray-300">${customer.email}</div>
        <div class="text-sm text-gray-400">${customer.phone}</div>
      </td>
      <td class="table-cell">
        <div class="text-sm text-gray-300">${customer.address}</div>
      </td>
      <td class="table-cell">
        <span class="status-badge ${customer.status === 'active' ? 'status-running' : 'status-idle'}">
          ${customer.status === 'active' ? 'Aktivní' : 'Neaktivní'}
        </span>
      </td>
      <td class="table-cell">
        <div class="text-sm text-gray-300">${customer.totalOrders}</div>
      </td>
      <td class="table-cell">
        <div class="text-sm font-medium text-white">${customer.totalValue.toLocaleString('cs-CZ')} Kč</div>
      </td>
      <td class="table-cell">
        <div class="flex space-x-2">
          <button class="text-blue-400 hover:text-blue-300 text-sm font-medium" onclick="editCustomer(${customer.id})">
            Upravit
          </button>
          <button class="text-gray-400 hover:text-gray-300 text-sm font-medium" onclick="viewCustomer(${customer.id})">
            Detail
          </button>
        </div>
      </td>
    </tr>
  `).join('')
}

function showAddCustomerModal() {
  alert('Funkce přidání zákazníka - zde by se otevřel modal pro přidání nového zákazníka')
}

function editCustomer(customerId) {
  const customer = customers.find(c => c.id === customerId)
  if (customer) {
    alert(`Úprava zákazníka: ${customer.name}\nZde by se otevřel formulář pro úpravu zákazníka.`)
  }
}

function viewCustomer(customerId) {
  const customer = customers.find(c => c.id === customerId)
  if (customer) {
    alert(`Detail zákazníka: ${customer.name}\nEmail: ${customer.email}\nTelefon: ${customer.phone}\nCelková hodnota: ${customer.totalValue.toLocaleString('cs-CZ')} Kč`)
  }
}

// Make functions globally available
window.editCustomer = editCustomer
window.viewCustomer = viewCustomer
