import { invoices } from '../data/mockData.js'

export function renderInvoices() {
  return `
    <div class="space-y-6">
      <!-- Header with Actions -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Faktury</h2>
          <p class="text-gray-600">Spr<PERSON>va faktur a plateb</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Export PDF
          </button>
          <button class="btn-primary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nová faktura
          </button>
        </div>
      </div>
      
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Celkem faktur</p>
              <p class="text-2xl font-semibold text-gray-900">${invoices.length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Zaplacené</p>
              <p class="text-2xl font-semibold text-gray-900">${invoices.filter(i => i.status === 'paid').length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-orange-100 text-orange-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Čekající platbu</p>
              <p class="text-2xl font-semibold text-gray-900">${invoices.filter(i => i.status === 'pending').length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Celková hodnota</p>
              <p class="text-2xl font-semibold text-gray-900">${invoices.reduce((sum, i) => sum + i.total, 0).toLocaleString('cs-CZ')} Kč</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Search and Filters -->
      <div class="card">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div class="flex-1 max-w-md">
            <div class="relative">
              <input type="text" placeholder="Hledat faktury..." 
                     class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
              <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex space-x-3">
            <select class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
              <option>Všechny stavy</option>
              <option>Zaplacené</option>
              <option>Čekající platbu</option>
              <option>Po splatnosti</option>
            </select>
            <input type="date" class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-primary-500">
          </div>
        </div>
      </div>
      
      <!-- Invoices Table -->
      <div class="card p-0">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="table-header">Číslo faktury</th>
                <th class="table-header">Zákazník</th>
                <th class="table-header">Datum vystavení</th>
                <th class="table-header">Datum splatnosti</th>
                <th class="table-header">Stav</th>
                <th class="table-header">Částka</th>
                <th class="table-header">Akce</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              ${invoices.map(invoice => `
                <tr class="hover:bg-gray-50">
                  <td class="table-cell">
                    <div class="text-sm font-medium text-gray-900">${invoice.id}</div>
                    <div class="text-xs text-gray-500">Objednávka #${invoice.orderId}</div>
                  </td>
                  <td class="table-cell">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                        ${invoice.customerName.charAt(0)}
                      </div>
                      <div class="ml-3">
                        <div class="text-sm font-medium text-gray-900">${invoice.customerName}</div>
                        <div class="text-sm text-gray-500">ID: ${invoice.customerId}</div>
                      </div>
                    </div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-900">${new Date(invoice.issueDate).toLocaleDateString('cs-CZ')}</div>
                  </td>
                  <td class="table-cell">
                    <div class="text-sm text-gray-900">${new Date(invoice.dueDate).toLocaleDateString('cs-CZ')}</div>
                    ${isOverdue(invoice.dueDate, invoice.status) ? 
                      '<div class="text-xs text-red-600 font-medium">Po splatnosti</div>' : 
                      ''
                    }
                  </td>
                  <td class="table-cell">
                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getInvoiceStatusColor(invoice.status)}">
                      ${getInvoiceStatusText(invoice.status)}
                    </span>
                    ${invoice.paidDate ? 
                      `<div class="text-xs text-gray-500 mt-1">Zaplaceno: ${new Date(invoice.paidDate).toLocaleDateString('cs-CZ')}</div>` : 
                      ''
                    }
                  </td>
                  <td class="table-cell">
                    <div class="text-sm font-medium text-gray-900">${invoice.total.toLocaleString('cs-CZ')} Kč</div>
                  </td>
                  <td class="table-cell">
                    <div class="flex space-x-2">
                      <button class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        PDF
                      </button>
                      <button class="text-gray-600 hover:text-gray-700 text-sm font-medium">
                        Detail
                      </button>
                      ${invoice.status === 'pending' ? 
                        '<button class="text-green-600 hover:text-green-700 text-sm font-medium">Označit jako zaplaceno</button>' : 
                        ''
                      }
                    </div>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card text-center">
          <svg class="w-12 h-12 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Vystavit fakturu</h3>
          <p class="text-gray-500 mb-4">Vytvořte novou fakturu pro zákazníka</p>
          <button class="btn-primary w-full">Nová faktura</button>
        </div>
        
        <div class="card text-center">
          <svg class="w-12 h-12 text-green-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Zaznamenat platbu</h3>
          <p class="text-gray-500 mb-4">Označte fakturu jako zaplacenou</p>
          <button class="btn-primary w-full">Zaznamenat platbu</button>
        </div>
        
        <div class="card text-center">
          <svg class="w-12 h-12 text-purple-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">Přehled plateb</h3>
          <p class="text-gray-500 mb-4">Zobrazit detailní přehled plateb</p>
          <button class="btn-primary w-full">Zobrazit přehled</button>
        </div>
      </div>
    </div>
  `
}

function getInvoiceStatusColor(status) {
  const colors = {
    paid: 'bg-green-100 text-green-800',
    pending: 'bg-orange-100 text-orange-800',
    overdue: 'bg-red-100 text-red-800'
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

function getInvoiceStatusText(status) {
  const texts = {
    paid: 'Zaplaceno',
    pending: 'Čekající platbu',
    overdue: 'Po splatnosti'
  }
  return texts[status] || status
}

function isOverdue(dueDate, status) {
  if (status === 'paid') return false
  return new Date(dueDate) < new Date()
}
