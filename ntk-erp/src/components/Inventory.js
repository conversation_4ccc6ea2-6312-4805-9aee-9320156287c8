import { inventory, products } from '../data/mockData.js'

export function renderInventory() {
  const totalCapacity = inventory.reduce((sum, loc) => 
    sum + loc.zones.reduce((zoneSum, zone) => zoneSum + zone.capacity, 0), 0)
  const totalOccupied = inventory.reduce((sum, loc) => 
    sum + loc.zones.reduce((zoneSum, zone) => zoneSum + zone.occupied, 0), 0)
  const utilizationRate = Math.round((totalOccupied / totalCapacity) * 100)
  
  return `
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-white">S<PERSON><PERSON><PERSON><PERSON> hospodářství</h2>
          <p class="text-gray-400"><PERSON><PERSON><PERSON><PERSON> zásob a skladových lokací</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
            </svg>
            Inventura
          </button>
          <button class="btn-primary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Příjem zboží
          </button>
        </div>
      </div>
      
      <!-- Warehouse Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Celková kapacita</p>
              <p class="text-2xl font-bold text-primary-400">${totalCapacity.toLocaleString('cs-CZ')} m</p>
            </div>
            <div class="w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Obsazeno</p>
              <p class="text-2xl font-bold text-success-400">${totalOccupied.toLocaleString('cs-CZ')} m</p>
            </div>
            <div class="w-12 h-12 bg-success-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-success-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Využití</p>
              <p class="text-2xl font-bold text-warning-400">${utilizationRate}%</p>
            </div>
            <div class="w-12 h-12 bg-warning-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-warning-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Nízké zásoby</p>
              <p class="text-2xl font-bold text-danger-400">${products.filter(p => p.stock <= p.minStock).length}</p>
            </div>
            <div class="w-12 h-12 bg-danger-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-danger-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Warehouse Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        ${inventory.map(warehouse => `
          <div class="card">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-white">${warehouse.location}</h3>
              <div class="text-sm text-gray-400">
                ${warehouse.zones.length} zón
              </div>
            </div>
            
            <div class="space-y-3">
              ${warehouse.zones.map(zone => {
                const utilization = Math.round((zone.occupied / zone.capacity) * 100)
                return `
                  <div class="bg-dark-700 rounded-lg p-4">
                    <div class="flex items-center justify-between mb-2">
                      <div>
                        <h4 class="text-white font-medium">${zone.name}</h4>
                        <p class="text-gray-400 text-sm">${zone.id}</p>
                      </div>
                      <div class="text-right">
                        <p class="text-white font-medium">${utilization}%</p>
                        <p class="text-gray-400 text-xs">${zone.items} položek</p>
                      </div>
                    </div>
                    
                    <div class="mb-2">
                      <div class="flex justify-between text-sm mb-1">
                        <span class="text-gray-400">Obsazenost</span>
                        <span class="text-white">${zone.occupied}/${zone.capacity} m</span>
                      </div>
                      <div class="progress-bar">
                        <div class="progress-fill ${getUtilizationColor(utilization)}" style="width: ${utilization}%"></div>
                      </div>
                    </div>
                    
                    <div class="flex justify-between items-center">
                      <span class="status-badge ${getZoneStatusClass(utilization)}">
                        ${getZoneStatusText(utilization)}
                      </span>
                      <button class="text-primary-400 hover:text-primary-300 text-sm font-medium">
                        Detail
                      </button>
                    </div>
                  </div>
                `
              }).join('')}
            </div>
          </div>
        `).join('')}
      </div>
      
      <!-- Stock Levels -->
      <div class="card">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-semibold text-white">Stav zásob</h3>
          <div class="flex space-x-2">
            <button class="btn-secondary text-sm">
              <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
              </svg>
              Filtr
            </button>
          </div>
        </div>
        
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-dark-700">
            <thead class="bg-dark-800">
              <tr>
                <th class="table-header text-left">Produkt</th>
                <th class="table-header text-left">SKU</th>
                <th class="table-header text-left">Lokace</th>
                <th class="table-header text-right">Skladem</th>
                <th class="table-header text-right">Min. zásoba</th>
                <th class="table-header text-center">Stav</th>
                <th class="table-header text-center">Akce</th>
              </tr>
            </thead>
            <tbody class="bg-dark-800 divide-y divide-dark-700">
              ${products.map(product => `
                <tr class="hover:bg-dark-700 transition-colors duration-200">
                  <td class="table-cell">
                    <div>
                      <div class="text-white font-medium">${product.name}</div>
                      <div class="text-gray-400 text-sm">${product.category}</div>
                    </div>
                  </td>
                  <td class="table-cell">
                    <span class="text-gray-300 font-mono text-sm">${product.sku}</span>
                  </td>
                  <td class="table-cell">
                    <span class="text-gray-300">${product.location}</span>
                  </td>
                  <td class="table-cell text-right">
                    <div class="text-white font-medium">${product.stock.toLocaleString('cs-CZ')} ${product.stockUnit}</div>
                  </td>
                  <td class="table-cell text-right">
                    <div class="text-gray-400">${product.minStock.toLocaleString('cs-CZ')} ${product.stockUnit}</div>
                  </td>
                  <td class="table-cell text-center">
                    <span class="status-badge ${getStockStatusClass(product.stock, product.minStock)}">
                      ${getStockStatusText(product.stock, product.minStock)}
                    </span>
                  </td>
                  <td class="table-cell text-center">
                    <div class="flex justify-center space-x-2">
                      <button class="text-primary-400 hover:text-primary-300 text-sm font-medium">
                        Pohyb
                      </button>
                      <button class="text-gray-400 hover:text-gray-300 text-sm font-medium">
                        Detail
                      </button>
                    </div>
                  </td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card text-center">
          <svg class="w-12 h-12 text-primary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"></path>
          </svg>
          <h3 class="text-lg font-medium text-white mb-2">Příjem zboží</h3>
          <p class="text-gray-400 mb-4">Zaznamenat nový příjem do skladu</p>
          <button class="btn-primary w-full">Nový příjem</button>
        </div>
        
        <div class="card text-center">
          <svg class="w-12 h-12 text-success-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
          </svg>
          <h3 class="text-lg font-medium text-white mb-2">Výdej zboží</h3>
          <p class="text-gray-400 mb-4">Zaznamenat výdej ze skladu</p>
          <button class="btn-primary w-full">Nový výdej</button>
        </div>
        
        <div class="card text-center">
          <svg class="w-12 h-12 text-warning-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
          </svg>
          <h3 class="text-lg font-medium text-white mb-2">Inventura</h3>
          <p class="text-gray-400 mb-4">Spustit inventuru skladu</p>
          <button class="btn-primary w-full">Spustit inventuru</button>
        </div>
      </div>
    </div>
  `
}

function getUtilizationColor(utilization) {
  if (utilization >= 90) return 'bg-danger-500'
  if (utilization >= 70) return 'bg-warning-500'
  return 'bg-success-500'
}

function getZoneStatusClass(utilization) {
  if (utilization >= 90) return 'status-danger'
  if (utilization >= 70) return 'status-warning'
  return 'status-running'
}

function getZoneStatusText(utilization) {
  if (utilization >= 90) return 'Plný'
  if (utilization >= 70) return 'Vysoké využití'
  return 'Dostupný'
}

function getStockStatusClass(stock, minStock) {
  if (stock <= minStock * 0.5) return 'status-danger'
  if (stock <= minStock) return 'status-warning'
  return 'status-running'
}

function getStockStatusText(stock, minStock) {
  if (stock <= minStock * 0.5) return 'Kritické'
  if (stock <= minStock) return 'Nízké'
  return 'Dostačující'
}
