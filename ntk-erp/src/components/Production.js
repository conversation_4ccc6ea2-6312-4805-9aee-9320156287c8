import { productionPlan, machines, dashboardStats } from '../data/mockData.js'

export function renderProduction() {
  return `
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-white">Výrobn<PERSON> plán</h2>
          <p class="text-gray-400">Plánování a sledování výroby</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
            </svg>
            <PERSON><PERSON><PERSON><PERSON><PERSON>
          </button>
          <button class="btn-primary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nový plán
          </button>
        </div>
      </div>
      
      <!-- Production Stats -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Denní výroba</p>
              <p class="text-2xl font-bold text-primary-400">${dashboardStats.dailyProduction.toLocaleString('cs-CZ')} m</p>
            </div>
            <div class="w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Efektivita výroby</p>
              <p class="text-2xl font-bold text-success-400">${dashboardStats.productionEfficiency}%</p>
            </div>
            <div class="w-12 h-12 bg-success-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-success-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Aktivní úkoly</p>
              <p class="text-2xl font-bold text-warning-400">${productionPlan.filter(p => p.status === 'in_progress').length}</p>
            </div>
            <div class="w-12 h-12 bg-warning-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-warning-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Naplánované úkoly</p>
              <p class="text-2xl font-bold text-blue-400">${productionPlan.filter(p => p.status === 'scheduled').length}</p>
            </div>
            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Production Timeline -->
      <div class="card">
        <h3 class="text-lg font-semibold text-white mb-6">Výrobní plán</h3>
        <div class="space-y-4">
          ${productionPlan.map(plan => `
            <div class="bg-dark-700 rounded-lg p-4 border-l-4 ${getStatusBorderColor(plan.status)}">
              <div class="flex items-start justify-between">
                <div class="flex-1">
                  <div class="flex items-center space-x-3 mb-2">
                    <h4 class="text-white font-medium">${plan.product}</h4>
                    <span class="status-badge ${getProductionStatusClass(plan.status)}">
                      ${getProductionStatusText(plan.status)}
                    </span>
                  </div>
                  
                  <div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                    <div>
                      <span class="text-gray-400">Objednávka:</span>
                      <span class="text-white ml-1">#${plan.orderId}</span>
                    </div>
                    <div>
                      <span class="text-gray-400">Množství:</span>
                      <span class="text-white ml-1">${plan.quantity.toLocaleString('cs-CZ')} ${plan.unit}</span>
                    </div>
                    <div>
                      <span class="text-gray-400">Stroj:</span>
                      <span class="text-white ml-1">${plan.machine}</span>
                    </div>
                    <div>
                      <span class="text-gray-400">Operátor:</span>
                      <span class="text-white ml-1">${plan.operator}</span>
                    </div>
                  </div>
                  
                  <div class="flex items-center justify-between mt-3">
                    <div class="text-sm">
                      <span class="text-gray-400">Termín:</span>
                      <span class="text-white ml-1">
                        ${new Date(plan.startDate).toLocaleDateString('cs-CZ')} - 
                        ${new Date(plan.endDate).toLocaleDateString('cs-CZ')}
                      </span>
                    </div>
                    <div class="text-sm">
                      <span class="text-gray-400">Pokrok:</span>
                      <span class="text-white ml-1">${plan.progress}%</span>
                    </div>
                  </div>
                  
                  <!-- Progress Bar -->
                  <div class="mt-3">
                    <div class="progress-bar">
                      <div class="progress-fill ${getProgressColor(plan.progress)}" style="width: ${plan.progress}%"></div>
                    </div>
                  </div>
                </div>
                
                <div class="flex space-x-2 ml-4">
                  <button class="btn-secondary text-sm px-3 py-1">
                    Detail
                  </button>
                  ${plan.status === 'scheduled' ? `
                    <button class="btn-success text-sm px-3 py-1">
                      Start
                    </button>
                  ` : plan.status === 'in_progress' ? `
                    <button class="btn-warning text-sm px-3 py-1">
                      Pozastavit
                    </button>
                  ` : ''}
                </div>
              </div>
            </div>
          `).join('')}
        </div>
      </div>
      
      <!-- Machine Status Overview -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div class="card">
          <h3 class="text-lg font-semibold text-white mb-4">Stav strojů</h3>
          <div class="space-y-3">
            ${machines.map(machine => `
              <div class="flex items-center justify-between p-3 bg-dark-700 rounded-lg">
                <div class="flex items-center space-x-3">
                  <div class="w-3 h-3 rounded-full ${getMachineStatusColor(machine.status)}"></div>
                  <div>
                    <p class="text-white font-medium">${machine.name}</p>
                    <p class="text-gray-400 text-sm">${machine.currentProduct || 'Nečinný'}</p>
                  </div>
                </div>
                <div class="text-right">
                  <p class="text-white font-medium">${machine.efficiency}%</p>
                  <p class="text-gray-400 text-sm">Efektivita</p>
                </div>
              </div>
            `).join('')}
          </div>
        </div>
        
        <div class="card">
          <h3 class="text-lg font-semibold text-white mb-4">Denní výroba</h3>
          <div class="h-64 bg-dark-700 rounded-lg flex items-center justify-center">
            <div class="text-center">
              <svg class="w-16 h-16 text-gray-500 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
              <p class="text-gray-400">Graf výroby</p>
              <p class="text-sm text-gray-500">Zde by byl graf denní výroby</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
}

function getStatusBorderColor(status) {
  const colors = {
    in_progress: 'border-success-500',
    scheduled: 'border-blue-500',
    completed: 'border-primary-500',
    delayed: 'border-danger-500'
  }
  return colors[status] || 'border-gray-500'
}

function getProductionStatusClass(status) {
  const classes = {
    in_progress: 'status-running',
    scheduled: 'bg-blue-500/20 text-blue-400 border border-blue-500/30',
    completed: 'bg-primary-500/20 text-primary-400 border border-primary-500/30',
    delayed: 'status-danger'
  }
  return classes[status] || 'status-idle'
}

function getProductionStatusText(status) {
  const texts = {
    in_progress: 'Probíhá',
    scheduled: 'Naplánováno',
    completed: 'Dokončeno',
    delayed: 'Zpožděno'
  }
  return texts[status] || status
}

function getProgressColor(progress) {
  if (progress >= 80) return 'bg-success-500'
  if (progress >= 50) return 'bg-warning-500'
  return 'bg-primary-500'
}

function getMachineStatusColor(status) {
  const colors = {
    running: 'bg-success-500 animate-pulse',
    maintenance: 'bg-blue-500',
    warning: 'bg-warning-500',
    idle: 'bg-gray-500',
    error: 'bg-danger-500'
  }
  return colors[status] || 'bg-gray-500'
}
