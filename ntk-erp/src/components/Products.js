import { products } from '../data/mockData.js'

export function renderProducts() {
  // Add event listeners after render
  setTimeout(() => {
    addProductEventListeners()
  }, 100)

  return `
    <div class="space-y-6">
      <!-- Header with Actions -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-gray-900">Produkty</h2>
          <p class="text-gray-600">Správa produktového katalogu</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Import
          </button>
          <button class="btn-primary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Přidat produkt
          </button>
        </div>
      </div>
      
      <!-- Stats Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-blue-100 text-blue-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Celkem produktů</p>
              <p class="text-2xl font-semibold text-gray-900">${products.length}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-green-100 text-green-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Skladem</p>
              <p class="text-2xl font-semibold text-gray-900">${products.reduce((sum, p) => sum + p.stock, 0)}</p>
            </div>
          </div>
        </div>
        
        <div class="card">
          <div class="flex items-center">
            <div class="p-3 rounded-full bg-purple-100 text-purple-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-gray-600">Kategorie</p>
              <p class="text-2xl font-semibold text-gray-900">${[...new Set(products.map(p => p.category))].length}</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Search and Filters -->
      <div class="card">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div class="flex-1 max-w-md">
            <div class="relative">
              <input type="text" placeholder="Hledat kabely..."
                     class="input-field pl-10 pr-4">
              <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </div>
          </div>
          <div class="flex space-x-3">
            <select class="select-field">
              <option>Všechny kategorie</option>
              <option>Silové kabely</option>
              <option>Datové kabely</option>
              <option>Optické kabely</option>
              <option>Koaxiální kabely</option>
              <option>Řídicí kabely</option>
            </select>
            <select class="select-field">
              <option>Seřadit podle</option>
              <option>Název A-Z</option>
              <option>Název Z-A</option>
              <option>Cena vzestupně</option>
              <option>Cena sestupně</option>
              <option>Skladem</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- Products Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        ${products.map(product => `
          <div class="card hover:shadow-xl transition-all duration-200">
            <div class="flex items-start justify-between mb-4">
              <div class="flex-1">
                <h3 class="text-lg font-semibold text-white mb-1">${product.name}</h3>
                <p class="text-sm text-gray-400 mb-2">SKU: ${product.sku}</p>
                <span class="status-badge bg-blue-500/20 text-blue-400 border border-blue-500/30">
                  ${product.category}
                </span>
              </div>
              <div class="text-right">
                <p class="text-2xl font-bold text-white">${product.price.toLocaleString('cs-CZ')} Kč</p>
              </div>
            </div>

            <p class="text-gray-300 text-sm mb-4">${product.description}</p>

            <div class="flex items-center justify-between mb-4">
              <div class="flex items-center">
                <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                <span class="text-sm text-gray-300">Skladem: ${product.stock} ks</span>
              </div>
              <span class="status-badge ${
                product.stock > 10 ? 'status-running' :
                product.stock > 5 ? 'status-warning' :
                'status-danger'
              }">
                ${product.stock > 10 ? 'Dostupné' : product.stock > 5 ? 'Málo skladem' : 'Kritické'}
              </span>
            </div>

            <div class="text-xs text-gray-400 mb-4">
              Dodavatel: ${product.supplier}
            </div>

            <div class="flex space-x-2">
              <button class="flex-1 btn-primary text-sm py-2" onclick="editProduct('${product.sku}')">
                Upravit
              </button>
              <button class="flex-1 btn-secondary text-sm py-2" onclick="viewProduct('${product.sku}')">
                Detail
              </button>
            </div>
          </div>
        `).join('')}
      </div>
      
      <!-- Add more products placeholder -->
      <div class="card border-2 border-dashed border-slate-600 text-center py-12 hover:border-blue-500 transition-colors duration-200">
        <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
        </svg>
        <h3 class="text-lg font-medium text-white mb-2">Přidat nový kabel</h3>
        <p class="text-gray-400 mb-4">Rozšiřte svůj katalog kabelů</p>
        <button class="btn-primary" onclick="addNewProduct()">
          Přidat kabel
        </button>
      </div>
    </div>
  `
}

function addProductEventListeners() {
  // Import button
  const importBtn = document.querySelector('button:contains("Import")')
  if (importBtn) {
    importBtn.addEventListener('click', () => {
      alert('Import produktů - zde by se otevřel dialog pro import CSV/Excel souboru s produkty.')
    })
  }

  // Add product button in header
  const addProductHeaderBtn = document.querySelector('button:contains("Přidat produkt")')
  if (addProductHeaderBtn) {
    addProductHeaderBtn.addEventListener('click', () => {
      addNewProduct()
    })
  }
}

function editProduct(sku) {
  const product = products.find(p => p.sku === sku)
  if (product) {
    alert(`Úprava kabelu: ${product.name}\nSKU: ${product.sku}\nCena: ${product.price.toLocaleString('cs-CZ')} Kč\n\nZde by se otevřel formulář pro úpravu produktu.`)
  }
}

function viewProduct(sku) {
  const product = products.find(p => p.sku === sku)
  if (product) {
    alert(`Detail kabelu: ${product.name}\n\nSKU: ${product.sku}\nKategorie: ${product.category}\nCena: ${product.price.toLocaleString('cs-CZ')} Kč\nSkladem: ${product.stock} ks\nDodavatel: ${product.supplier}\n\nPopis: ${product.description}`)
  }
}

function addNewProduct() {
  const productName = prompt('Zadejte název nového kabelu:')
  if (productName) {
    alert(`Nový kabel "${productName}" bude přidán do katalogu.\n\nZde by se otevřel formulář pro zadání všech parametrů kabelu (SKU, cena, kategorie, specifikace, atd.).`)
  }
}

// Make functions globally available
window.editProduct = editProduct
window.viewProduct = viewProduct
window.addNewProduct = addNewProduct
