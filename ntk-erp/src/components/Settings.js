export function renderSettings() {
  // Add event listeners after render
  setTimeout(() => {
    addSettingsEventListeners()
  }, 100)

  return `
    <div class="space-y-6">
      <!-- Header -->
      <div>
        <h2 class="text-2xl font-bold text-gray-900">Nastavení</h2>
        <p class="text-gray-600">Konfigurace systému a uživatelských preferencí</p>
      </div>
      
      <!-- Settings Sections -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Company Settings -->
        <div class="lg:col-span-2 space-y-6">
          <div class="card">
            <h3 class="text-lg font-semibold text-white mb-4">Informace o společnosti</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Ná<PERSON><PERSON> společnosti</label>
                <input type="text" value="NKT s.r.o." class="input-field">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">IČO</label>
                <input type="text" value="12345678" class="input-field">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">DIČ</label>
                <input type="text" value="CZ12345678" class="input-field">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Telefon</label>
                <input type="text" value="+420 123 456 789" class="input-field">
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-300 mb-2">Adresa</label>
                <input type="text" value="Václavské náměstí 1, 110 00 Praha 1" class="input-field">
              </div>
              <div class="md:col-span-2">
                <label class="block text-sm font-medium text-gray-300 mb-2">Email</label>
                <input type="email" value="<EMAIL>" class="input-field">
              </div>
            </div>
            <div class="mt-6">
              <button class="btn-primary">Uložit změny</button>
            </div>
          </div>
          
          <!-- Invoice Settings -->
          <div class="card">
            <h3 class="text-lg font-semibold text-white mb-4">Nastavení fakturace</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Prefix faktur</label>
                <input type="text" value="FAK-" class="input-field">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Splatnost (dny)</label>
                <input type="number" value="30" class="input-field">
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Měna</label>
                <select class="select-field">
                  <option>CZK - Česká koruna</option>
                  <option>EUR - Euro</option>
                  <option>USD - Americký dolar</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">DPH (%)</label>
                <input type="number" value="21" class="input-field">
              </div>
            </div>
            <div class="mt-4">
              <label class="flex items-center">
                <input type="checkbox" checked class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
                <span class="ml-2 text-sm text-gray-300">Automaticky odesílat faktury emailem</span>
              </label>
            </div>
            <div class="mt-6">
              <button class="btn-primary">Uložit nastavení</button>
            </div>
          </div>
          
          <!-- System Settings -->
          <div class="card">
            <h3 class="text-lg font-semibold text-white mb-4">Systémová nastavení</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Jazyk systému</label>
                <select class="select-field">
                  <option>Čeština</option>
                  <option>English</option>
                  <option>Slovenčina</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Časové pásmo</label>
                <select class="select-field">
                  <option>Europe/Prague</option>
                  <option>Europe/London</option>
                  <option>America/New_York</option>
                </select>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-300 mb-2">Formát data</label>
                <select class="select-field">
                  <option>DD.MM.YYYY</option>
                  <option>MM/DD/YYYY</option>
                  <option>YYYY-MM-DD</option>
                </select>
              </div>
              <div class="space-y-2">
                <label class="flex items-center">
                  <input type="checkbox" checked class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
                  <span class="ml-2 text-sm text-gray-300">Povolit emailové notifikace</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
                  <span class="ml-2 text-sm text-gray-300">Povolit push notifikace</span>
                </label>
                <label class="flex items-center">
                  <input type="checkbox" checked class="rounded border-slate-600 text-blue-600 focus:ring-blue-500 bg-slate-700">
                  <span class="ml-2 text-sm text-gray-300">Automatické zálohování dat</span>
                </label>
              </div>
            </div>
            <div class="mt-6">
              <button class="btn-primary">Uložit nastavení</button>
            </div>
          </div>
        </div>
        
        <!-- Quick Actions Sidebar -->
        <div class="space-y-6">
          <!-- User Profile -->
          <div class="card">
            <h3 class="text-lg font-semibold text-white mb-4">Uživatelský profil</h3>
            <div class="text-center">
              <div class="w-20 h-20 bg-blue-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                A
              </div>
              <h4 class="text-lg font-medium text-white">Admin</h4>
              <p class="text-gray-300"><EMAIL></p>
              <p class="text-sm text-gray-400 mt-1">Systémový administrátor</p>
            </div>
            <div class="mt-6 space-y-2">
              <button class="w-full btn-secondary text-sm">Změnit heslo</button>
              <button class="w-full btn-secondary text-sm">Upravit profil</button>
            </div>
          </div>
          
          <!-- System Info -->
          <div class="card">
            <h3 class="text-lg font-semibold text-white mb-4">Informace o systému</h3>
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-400">Verze:</span>
                <span class="font-medium text-white">2.1.0</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">Poslední aktualizace:</span>
                <span class="font-medium text-white">15.1.2024</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">Databáze:</span>
                <span class="font-medium text-green-400">Online</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-400">Úložiště:</span>
                <span class="font-medium text-white">2.1 GB / 10 GB</span>
              </div>
            </div>
            <div class="mt-4">
              <div class="w-full bg-slate-700 rounded-full h-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 21%"></div>
              </div>
              <p class="text-xs text-gray-400 mt-1">21% využito</p>
            </div>
          </div>
          
          <!-- Quick Actions -->
          <div class="card">
            <h3 class="text-lg font-semibold text-white mb-4">Rychlé akce</h3>
            <div class="space-y-2">
              <button class="w-full btn-secondary text-sm">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                Export dat
              </button>
              <button class="w-full btn-secondary text-sm">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0L8 8m4-4v12"></path>
                </svg>
                Záloha systému
              </button>
              <button class="w-full btn-secondary text-sm">
                <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                Zobrazit logy
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `
}

function addSettingsEventListeners() {
  // Save buttons
  const saveButtons = document.querySelectorAll('.btn-primary')
  saveButtons.forEach(btn => {
    if (btn.textContent.includes('Uložit')) {
      btn.addEventListener('click', (e) => {
        e.preventDefault()
        const section = btn.closest('.card').querySelector('h3').textContent
        alert(`Nastavení "${section}" bylo úspěšně uloženo!`)
      })
    }
  })

  // Profile buttons
  const changePasswordBtn = document.querySelector('button:contains("Změnit heslo")')
  const editProfileBtn = document.querySelector('button:contains("Upravit profil")')

  // Quick action buttons
  const quickActionButtons = document.querySelectorAll('.card:last-child .btn-secondary')
  quickActionButtons.forEach((btn, index) => {
    btn.addEventListener('click', () => {
      const actions = [
        'Export dat byl spuštěn. Soubor bude připraven ke stažení za několik minut.',
        'Záloha systému byla spuštěna. Proces může trvat několik minut.',
        'Systémové logy byly otevřeny v novém okně.'
      ]
      if (actions[index]) {
        alert(actions[index])
      }
    })
  })

  // Profile action buttons
  const profileButtons = document.querySelectorAll('.card .btn-secondary')
  profileButtons.forEach(btn => {
    if (btn.textContent.includes('Změnit heslo')) {
      btn.addEventListener('click', () => {
        const newPassword = prompt('Zadejte nové heslo:')
        if (newPassword) {
          alert('Heslo bylo úspěšně změněno!')
        }
      })
    } else if (btn.textContent.includes('Upravit profil')) {
      btn.addEventListener('click', () => {
        alert('Formulář pro úpravu profilu bude otevřen v novém okně.')
      })
    }
  })
}
