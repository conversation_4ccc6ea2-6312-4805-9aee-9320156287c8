import { machines } from '../data/mockData.js'

export function renderMachines() {
  return `
    <div class="space-y-6">
      <!-- Header -->
      <div class="flex justify-between items-center">
        <div>
          <h2 class="text-2xl font-bold text-white">Monitoring strojů</h2>
          <p class="text-gray-400"><PERSON><PERSON><PERSON><PERSON> výrobních linek a jejich stavu</p>
        </div>
        <div class="flex space-x-3">
          <button class="btn-secondary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            Obnovit
          </button>
          <button class="btn-primary">
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Nový stroj
          </button>
        </div>
      </div>
      
      <!-- Status Overview -->
      <div class="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Aktivní stroje</p>
              <p class="text-2xl font-bold text-success-400">${machines.filter(m => m.status === 'running').length}</p>
            </div>
            <div class="w-12 h-12 bg-success-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-success-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Údržba</p>
              <p class="text-2xl font-bold text-blue-400">${machines.filter(m => m.status === 'maintenance').length}</p>
            </div>
            <div class="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Varování</p>
              <p class="text-2xl font-bold text-warning-400">${machines.filter(m => m.status === 'warning').length}</p>
            </div>
            <div class="w-12 h-12 bg-warning-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-warning-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
          </div>
        </div>
        
        <div class="metric-card">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-gray-400 text-sm">Průměrná efektivita</p>
              <p class="text-2xl font-bold text-primary-400">${Math.round(machines.reduce((sum, m) => sum + m.efficiency, 0) / machines.length)}%</p>
            </div>
            <div class="w-12 h-12 bg-primary-500/20 rounded-lg flex items-center justify-center">
              <svg class="w-6 h-6 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Machines Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        ${machines.map(machine => `
          <div class="card hover:shadow-2xl transition-all duration-300 ${machine.status === 'running' ? 'ring-1 ring-success-500/30' : ''}">
            <div class="flex items-start justify-between mb-4">
              <div>
                <h3 class="text-lg font-semibold text-white mb-1">${machine.name}</h3>
                <p class="text-gray-400 text-sm">${machine.id} • ${machine.type}</p>
              </div>
              <span class="status-badge ${getStatusClass(machine.status)}">
                ${getStatusText(machine.status)}
              </span>
            </div>
            
            <!-- Machine Status Indicators -->
            <div class="grid grid-cols-2 gap-4 mb-4">
              <div class="bg-dark-700 rounded-lg p-3">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-gray-400 text-xs">Efektivita</span>
                  <span class="text-white font-medium">${machine.efficiency}%</span>
                </div>
                <div class="progress-bar">
                  <div class="progress-fill ${getEfficiencyColor(machine.efficiency)}" style="width: ${machine.efficiency}%"></div>
                </div>
              </div>
              
              <div class="bg-dark-700 rounded-lg p-3">
                <div class="flex items-center justify-between mb-1">
                  <span class="text-gray-400 text-xs">Rychlost</span>
                  <span class="text-white font-medium">${machine.speed} ${machine.speedUnit}</span>
                </div>
                <div class="text-xs text-gray-500">
                  ${machine.status === 'running' ? 'Běží' : 'Zastaveno'}
                </div>
              </div>
            </div>
            
            ${machine.temperature ? `
              <div class="bg-dark-700 rounded-lg p-3 mb-4">
                <div class="flex items-center justify-between">
                  <span class="text-gray-400 text-sm">Teplota</span>
                  <span class="text-white font-medium">${machine.temperature}°C</span>
                </div>
              </div>
            ` : ''}
            
            <!-- Current Production -->
            <div class="mb-4">
              <div class="flex items-center justify-between mb-2">
                <span class="text-gray-400 text-sm">Aktuální výroba</span>
                <span class="text-white text-sm">${machine.totalProduced} ${machine.producedUnit}</span>
              </div>
              ${machine.currentProduct ? `
                <div class="bg-primary-500/10 border border-primary-500/30 rounded-lg p-2">
                  <p class="text-primary-400 text-sm font-medium">${machine.currentProduct}</p>
                </div>
              ` : `
                <div class="bg-gray-500/10 border border-gray-500/30 rounded-lg p-2">
                  <p class="text-gray-400 text-sm">Žádná výroba</p>
                </div>
              `}
            </div>
            
            <!-- Operator & Shift -->
            <div class="flex items-center justify-between mb-4 text-sm">
              <div>
                <span class="text-gray-400">Operátor:</span>
                <span class="text-white ml-1">${machine.operator}</span>
              </div>
              <div>
                <span class="text-gray-400">Směna:</span>
                <span class="text-white ml-1">${machine.shift}</span>
              </div>
            </div>
            
            <!-- Alerts -->
            ${machine.alerts.length > 0 ? `
              <div class="mb-4">
                ${machine.alerts.map(alert => `
                  <div class="bg-warning-500/10 border border-warning-500/30 rounded-lg p-2 mb-2">
                    <div class="flex items-center">
                      <svg class="w-4 h-4 text-warning-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                      </svg>
                      <span class="text-warning-400 text-sm">${alert}</span>
                    </div>
                  </div>
                `).join('')}
              </div>
            ` : ''}
            
            <!-- Maintenance Info -->
            <div class="text-xs text-gray-500 mb-4">
              <div class="flex justify-between">
                <span>Poslední údržba:</span>
                <span>${new Date(machine.lastMaintenance).toLocaleDateString('cs-CZ')}</span>
              </div>
              <div class="flex justify-between">
                <span>Další údržba:</span>
                <span>${new Date(machine.nextMaintenance).toLocaleDateString('cs-CZ')}</span>
              </div>
            </div>
            
            <!-- Actions -->
            <div class="flex space-x-2">
              <button class="flex-1 btn-secondary text-sm py-2">
                Detail
              </button>
              ${machine.status === 'running' ? `
                <button class="flex-1 btn-danger text-sm py-2" onclick="stopMachine('${machine.id}')">
                  Stop
                </button>
              ` : machine.status === 'idle' ? `
                <button class="flex-1 btn-success text-sm py-2" onclick="startMachine('${machine.id}')">
                  Start
                </button>
              ` : `
                <button class="flex-1 btn-secondary text-sm py-2" onclick="viewMaintenance('${machine.id}')">
                  Údržba
                </button>
              `}
            </div>
          </div>
        `).join('')}
      </div>
    </div>
  `
}

function getStatusClass(status) {
  const classes = {
    running: 'status-running',
    maintenance: 'status-maintenance',
    warning: 'status-warning',
    idle: 'status-idle',
    error: 'status-danger'
  }
  return classes[status] || 'status-idle'
}

function getStatusText(status) {
  const texts = {
    running: 'Běží',
    maintenance: 'Údržba',
    warning: 'Varování',
    idle: 'Nečinný',
    error: 'Chyba'
  }
  return texts[status] || status
}

function getEfficiencyColor(efficiency) {
  if (efficiency >= 90) return 'bg-green-500'
  if (efficiency >= 70) return 'bg-yellow-500'
  return 'bg-red-500'
}

// Machine control functions
function startMachine(machineId) {
  const machine = machines.find(m => m.id === machineId)
  if (machine) {
    alert(`Spouštění stroje: ${machine.name}\nStroj bude spuštěn a začne výroba.`)
    // Here would be actual API call
  }
}

function stopMachine(machineId) {
  const machine = machines.find(m => m.id === machineId)
  if (machine) {
    if (confirm(`Opravdu chcete zastavit stroj: ${machine.name}?`)) {
      alert(`Stroj ${machine.name} byl zastaven.`)
      // Here would be actual API call
    }
  }
}

function viewMaintenance(machineId) {
  const machine = machines.find(m => m.id === machineId)
  if (machine) {
    alert(`Údržba stroje: ${machine.name}\nPoslední údržba: ${new Date(machine.lastMaintenance).toLocaleDateString('cs-CZ')}\nDalší údržba: ${new Date(machine.nextMaintenance).toLocaleDateString('cs-CZ')}`)
  }
}

// Make functions globally available
window.startMachine = startMachine
window.stopMachine = stopMachine
window.viewMaintenance = viewMaintenance
