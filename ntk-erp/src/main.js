import './style.css'
import { renderDashboard } from './components/Dashboard.js'
import { renderCustomers } from './components/Customers.js'
import { renderProducts } from './components/Products.js'
import { renderOrders } from './components/Orders.js'
import { renderInvoices } from './components/Invoices.js'
import { renderSettings } from './components/Settings.js'
import { renderMachines } from './components/Machines.js'
import { renderInventory } from './components/Inventory.js'
import { renderProduction } from './components/Production.js'

class Router {
  constructor() {
    this.routes = {
      '/': renderDashboard,
      '/dashboard': renderDashboard,
      '/machines': renderMachines,
      '/production': renderProduction,
      '/inventory': renderInventory,
      '/orders': renderOrders,
      '/customers': renderCustomers,
      '/products': renderProducts,
      '/invoices': renderInvoices,
      '/settings': renderSettings
    }
    
    this.init()
  }
  
  init() {
    // Handle initial load
    this.handleRoute()
    
    // Handle browser back/forward
    window.addEventListener('popstate', () => this.handleRoute())
    
    // Handle navigation clicks
    document.addEventListener('click', (e) => {
      if (e.target.matches('[data-route]')) {
        e.preventDefault()
        const route = e.target.getAttribute('data-route')
        this.navigate(route)
      }
    })
  }
  
  navigate(path) {
    window.history.pushState({}, '', path)
    this.handleRoute()
  }
  
  handleRoute() {
    const path = window.location.pathname
    const route = this.routes[path] || this.routes['/']
    
    // Render layout with content
    this.renderLayout(route)
    
    // Update active navigation
    this.updateActiveNav(path)
  }
  
  renderLayout(contentRenderer) {
    const app = document.getElementById('app')
    app.innerHTML = `
      <div class="flex h-screen bg-slate-900">
        <!-- Sidebar -->
        <div class="w-64 bg-slate-800 shadow-2xl border-r border-slate-700">
          <div class="p-6 border-b border-slate-700">
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
              </div>
              <div>
                <h1 class="text-xl font-bold text-white">NKT (TESTING)</h1>
                <p class="text-xs text-gray-400">Výrobní ERP</p>
              </div>
            </div>
          </div>
          <nav class="mt-6 px-2 sidebar-scrollbar overflow-y-auto max-h-[calc(100vh-120px)]">
            <a href="/" data-route="/" class="sidebar-link mb-1" data-nav="/">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 5a2 2 0 012-2h4a2 2 0 012 2v6H8V5z"></path>
              </svg>
              Dashboard
            </a>
            <a href="/machines" data-route="/machines" class="sidebar-link mb-1" data-nav="/machines">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              Stroje
            </a>
            <a href="/production" data-route="/production" class="sidebar-link mb-1" data-nav="/production">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"></path>
              </svg>
              Výroba
            </a>
            <a href="/inventory" data-route="/inventory" class="sidebar-link mb-1" data-nav="/inventory">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
              </svg>
              Sklad
            </a>
            <a href="/orders" data-route="/orders" class="sidebar-link mb-1" data-nav="/orders">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Objednávky
            </a>
            <a href="/customers" data-route="/customers" class="sidebar-link mb-1" data-nav="/customers">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
              </svg>
              Zákazníci
            </a>
            <a href="/products" data-route="/products" class="sidebar-link mb-1" data-nav="/products">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"></path>
              </svg>
              Kabely
            </a>
            <a href="/invoices" data-route="/invoices" class="sidebar-link mb-1" data-nav="/invoices">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
              </svg>
              Faktury
            </a>
            <a href="/settings" data-route="/settings" class="sidebar-link mb-1" data-nav="/settings">
              <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
              </svg>
              Nastavení
            </a>
          </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col">
          <header class="bg-slate-800 shadow-xl border-b border-slate-700 px-6 py-4 flex-shrink-0">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-4">
                <h2 class="text-xl font-semibold text-white" id="page-title">Dashboard</h2>
                <div class="flex items-center space-x-2 text-sm text-gray-400">
                  <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Systém online</span>
                </div>
              </div>
              <div class="flex items-center space-x-4">
                <div class="flex items-center space-x-2 text-sm text-gray-400">
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                  <span id="current-time">--:--</span>
                </div>
                <button class="btn-secondary text-sm">
                  <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 19h16"></path>
                  </svg>
                  Export
                </button>
                <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-medium shadow-lg">
                  A
                </div>
              </div>
            </div>
          </header>

          <main class="flex-1 overflow-y-auto p-6 bg-slate-900 custom-scrollbar" id="main-content">
            <!-- Content will be rendered here -->
          </main>
        </div>
      </div>
    `
    
    // Render the specific page content
    const mainContent = document.getElementById('main-content')
    mainContent.innerHTML = contentRenderer()
  }
  
  updateActiveNav(currentPath) {
    // Remove active class from all nav links
    document.querySelectorAll('[data-nav]').forEach(link => {
      link.classList.remove('active')
    })
    
    // Add active class to current nav link
    const activeLink = document.querySelector(`[data-nav="${currentPath}"]`) || 
                      document.querySelector(`[data-nav="/"]`)
    if (activeLink) {
      activeLink.classList.add('active')
    }
    
    // Update page title
    const titles = {
      '/': 'Dashboard',
      '/dashboard': 'Dashboard',
      '/machines': 'Stroje',
      '/production': 'Výroba',
      '/inventory': 'Sklad',
      '/orders': 'Objednávky',
      '/customers': 'Zákazníci',
      '/products': 'Kabely',
      '/invoices': 'Faktury',
      '/settings': 'Nastavení'
    }
    
    const pageTitle = document.getElementById('page-title')
    if (pageTitle) {
      pageTitle.textContent = titles[currentPath] || 'Dashboard'
    }
  }
}

// Initialize the router when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new Router()

  // Update time every second
  function updateTime() {
    const now = new Date()
    const timeString = now.toLocaleTimeString('cs-CZ', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    const timeElement = document.getElementById('current-time')
    if (timeElement) {
      timeElement.textContent = timeString
    }
  }

  updateTime()
  setInterval(updateTime, 1000)
})
